# `CmWechatGroupQueryService.getJoinGroupResult` 接口测试用例

## 1. 接口概述

- **接口类型:** `Dubbo`
- **接口名称:** `CmWechatGroupQueryService.getJoinGroupResult`
- **接口路径:** `com.howbuy.crm.wechat.client.producer.cmwechatgroup.CmWechatGroupQueryService.getJoinGroupResult(hbOneNo, groupId)`
- **功能描述:** 查询指定一账通用户在某群的加群结果（未入群/已入群/已退群），并返回入群时间与退群时间（若有）。

## 2. 依赖数据表范围
为了给接口准备真实有效的测试数据，需要确保以下 **3个** 表中的数据是相互关联且逻辑正确的。

| 表名 | 用途 | 关键字段 | 关联逻辑 |
| :--- | :--- | :--- | :--- |
| `cm_wechat_cust_info` | 存放客户与企业微信外部联系人ID的映射 | `HBONE_NO`, `EXTERNAL_USER_ID` | 通过 `HBONE_NO` 找到客户 `EXTERNAL_USER_ID` |
| `CM_WECHAT_GROUP_USER` | 存放客户在群内的成员关系与状态 | `CHAT_ID`, `EXTERNAL_USER_ID`, `USERCHATFLAG`, `JOIN_TIME`, `LEAVE_TIME` | 使用 `CHAT_ID` + `EXTERNAL_USER_ID` 查询客户在群内的当前/历史状态 |
| `cm_wechat_group` | 存放群信息，如是否解散 | `CHAT_ID`, `CHAT_FLAG`, `UPDATE_TIME` | 当 `LEAVE_TIME` 为空且群已解散(`CHAT_FLAG=1`)时，使用群的 `UPDATE_TIME` 作为退群时间 |

**数据准备核心思路:**
构造一条 `cm_wechat_cust_info` 记录以建立 `HBONE_NO -> EXTERNAL_USER_ID` 映射；在 `CM_WECHAT_GROUP_USER` 中插入与目标 `CHAT_ID` 的成员记录，设置 `USERCHATFLAG`、`JOIN_TIME`、可选 `LEAVE_TIME` 等字段；若需要覆盖“群已解散但成员记录无退群时间”的场景，则在 `cm_wechat_group` 中为该 `CHAT_ID` 设置 `CHAT_FLAG=1` 且 `UPDATE_TIME` 不为空。

## 3. 输入参数 (`getJoinGroupResult(hbOneNo, groupId)`)
| 参数名 | 类型 | 是否必填 | 描述 |
| :--- | :--- | :--- | :--- |
| `hbOneNo` | `String` | 是 | 一账通号，用于反查 `EXTERNAL_USER_ID` |
| `groupId` | `String` | 是 | 群ID（`CHAT_ID`） |

## 4. 输出结果 (`Response<JoinGroupResultVO>`)
- **成功:** 返回 `Response.ok(JoinGroupResultVO)`，其中 `JoinGroupResultVO` 包含：
  - `joinGroupStatus`: 加群状态（`1` 未入群，`2` 已入群，`3` 已退群）
  - `joinGroupTime`: 入群时间（yyyyMMddHHmmss），当查询到入群记录时返回
  - `existGroupTime`: 退群时间（yyyyMMddHHmmss），当记录存在退群时间或群已解散且有 `UPDATE_TIME` 时返回
- **失败:** 当 `hbOneNo` 或 `groupId` 为空时，返回 `Response`，`code=0002`（参数错误），`description` 为错误描述，`returnObject=null`。

## 5. 测试用例

### 5.1. 正常场景测试
| 用例ID | 用例标题 | 用例描述 | 前置条件 | 输入参数 | 输入参数json | 预期结果 |
| :--- | :--- | :--- | :--- | :--- | :--- | :--- |
| **TC-N-001** | 已入群-正常返回入群时间 | `HBONE_NO` 可映射到 `EXTERNAL_USER_ID`，且在 `CM_WECHAT_GROUP_USER` 中存在该 `CHAT_ID` + `EXTERNAL_USER_ID` 且 `USERCHATFLAG=0` 的记录 | 表数据已准备：存在 `cm_wechat_cust_info(HBONE_NO, EXTERNAL_USER_ID)`；存在 `CM_WECHAT_GROUP_USER(CHAT_ID, EXTERNAL_USER_ID, USERCHATFLAG=0, JOIN_TIME)` | `hbOneNo`: `有效值`；`groupId`: `有效值` | {"hbOneNo":"9000000011","groupId":"wrl2gbEQAA-xWt1NAK5iT6pbl-6JjHog"} | 1. `Response.code=0000`。<br>2. `returnObject.joinGroupStatus=2`。<br>3. `returnObject.joinGroupTime` 不为空。<br>4. `returnObject.existGroupTime` 为空。 |
| **TC-N-002** | 已退群-记录含退群时间 | `HBONE_NO` 可映射；存在 `CM_WECHAT_GROUP_USER` 记录，`USERCHATFLAG!=0`，且 `LEAVE_TIME` 不为空 | 同上，且 `LEAVE_TIME` 不为空 | `hbOneNo`: `有效值`；`groupId`: `有效值` | {"hbOneNo":"9200000103","groupId":"AWX000001"} | 1. `Response.code=0000`。<br>2. `returnObject.joinGroupStatus=3`。<br>3. `returnObject.joinGroupTime` 不为空。<br>4. `returnObject.existGroupTime` 等于记录的 `LEAVE_TIME`（yyyyMMddHHmmss）。 |
| **TC-N-003** | 已退群-记录无退群时间且群已解散 | `HBONE_NO` 可映射；存在 `CM_WECHAT_GROUP_USER` 记录，`USERCHATFLAG!=0`，`LEAVE_TIME` 为空；`cm_wechat_group.CHAT_FLAG=1` 且 `UPDATE_TIME` 不为空 | 同上，并在 `cm_wechat_group` 为该 `CHAT_ID` 设置 `CHAT_FLAG=1`, `UPDATE_TIME` 不为空 | `hbOneNo`: `有效值`；`groupId`: `有效值` | {"hbOneNo":"待准备数据","groupId":"待准备数据"} | 1. `Response.code=0000`。<br>2. `returnObject.joinGroupStatus=3`。<br>3. `returnObject.joinGroupTime` 不为空。<br>4. `returnObject.existGroupTime` 等于群的 `UPDATE_TIME`（yyyyMMddHHmmss）。 |
| **TC-N-004** | 未入群-无法映射外部联系人ID | `cm_wechat_cust_info` 中不存在该 `HBONE_NO` 的记录 | 无 | `hbOneNo`: `无映射值`；`groupId`: `有效值` | {"hbOneNo":"HBONE_NO_NOT_EXISTS_999999","groupId":"AWX000001"} | 1. `Response.code=0000`。<br>2. `returnObject.joinGroupStatus=1`。<br>3. `returnObject.joinGroupTime` 为空。<br>4. `returnObject.existGroupTime` 为空。 |
| **TC-N-005** | 未入群-群成员记录不存在 | 能映射出 `EXTERNAL_USER_ID`，但 `CM_WECHAT_GROUP_USER` 无该 `CHAT_ID` + `EXTERNAL_USER_ID` 记录 | 在 `cm_wechat_cust_info` 有映射；`CM_WECHAT_GROUP_USER` 无对应记录 | `hbOneNo`: `有效值`；`groupId`: `有效值` | {"hbOneNo":"9000000011","groupId":"AWX000001"} | 1. `Response.code=0000`。<br>2. `returnObject.joinGroupStatus=1`。<br>3. `returnObject.joinGroupTime` 为空。<br>4. `returnObject.existGroupTime` 为空。 |

### 5.2. 异常及边界场景测试
| 用例ID | 用例标题 | 用例描述 | 前置条件 | 输入参数 | 输入参数json | 预期结果 |
| :--- | :--- | :--- | :--- | :--- | :--- | :--- |
| **TC-E-001** | 参数缺失-hbOneNo为空 | `hbOneNo` 为空字符串或 `null` | 无 | `hbOneNo`: ``；`groupId`: `有效值` | {"hbOneNo":"","groupId":"AWX000001"} | 1. `Response.code=0002`。<br>2. `Response.description` 包含`参数错误`。<br>3. `returnObject=null`。 |
| **TC-E-002** | 参数缺失-groupId为空 | `groupId` 为空字符串或 `null` | 无 | `hbOneNo`: `有效值`；`groupId`: `` | {"hbOneNo":"9000000011","groupId":""} | 1. `Response.code=0002`。<br>2. `Response.description` 包含`参数错误`。<br>3. `returnObject=null`。 |


