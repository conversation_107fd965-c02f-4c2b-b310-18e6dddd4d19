# `CmWechatGroupQueryService.getGroupIdByHbOneNo` 接口测试用例

## 1. 接口概述

- **接口类型:** `Dubbo`
- **接口名称:** `CmWechatGroupQueryService.getGroupIdByHbOneNo`
- **接口路径:** `com.howbuy.crm.wechat.client.producer.cmwechatgroup.CmWechatGroupQueryService.getGroupIdByHbOneNo(String)`
- **功能描述:** 根据好买一账通号(hbOneNo)查询并返回其所属或默认微信群ID信息。

## 2. 依赖数据表范围
为了给接口准备真实有效的测试数据，需要确保以下 **3个** 表中的数据是相互关联且逻辑正确的。

| 表名 | 用途 | 关键字段 | 关联逻辑 |
| :--- | :--- | :--- | :--- |
| `cm_wechat_cust_info` | 存储微信客户与账户的绑定信息 | `id`, `hb_one_no`, `open_id` | 通过 `hb_one_no` 关联用户与群关系表中的 `hb_one_no` |
| `cm_wechat_group` | 存储微信群的基础信息 | `id`, `group_id`, `status` | 通过 `group_id` 与用户群关系表中的 `group_id` 关联 |
| `cm_wechat_group_user_rel` | 存储客户与微信群的关系 | `id`, `hb_one_no`, `group_id`, `is_default` | 同一 `hb_one_no` 可对应多个 `group_id`，默认群 `is_default=1` |

**数据准备核心思路:**
为某个测试用户创建一条 `cm_wechat_cust_info` 记录（包含有效的 `hb_one_no`），并在 `cm_wechat_group` 中准备至少一个有效群（`status=normal`）。在 `cm_wechat_group_user_rel` 中建立该 `hb_one_no` 与目标 `group_id` 的关系记录（可设置一个为默认群）。用于不同测试用例，分别构造“存在默认群”“不存在群关系”“无默认群但存在群关系”等数据场景。

## 3. 输入参数 (`hbOneNo`)
| 参数名 | 类型 | 是否必填 | 描述 |
| :--- | :--- | :--- | :--- |
| `hbOneNo` | `String` | 是 | 好买一账通号，作为客户唯一业务标识 |

## 4. 输出结果 (`Response<GroupIdVO>`) 
- **成功:** 返回 `Response.ok(Data)`，其中 `Data` 为 `GroupIdVO`，包含用户所属或默认微信群标识（如 `groupId` 等字段）。当未查询到群关系时，`Data` 可能为 `null`。
- **失败:** 发生参数异常或系统异常时，返回带有错误码和错误信息的 `Response` 对象（例如：`ResponseCodeEnum.PARAM_ERROR`、`ResponseCodeEnum.SYS_ERROR`）。

## 5. 测试用例

### 5.1. 正常场景测试
| 用例ID | 用例标题 | 用例描述 | 前置条件 | 输入参数 | 输入参数json | 预期结果 |
| :--- | :--- | :--- | :--- | :--- | :--- | :--- |
| **TC-N-001** | 查询到默认群 | 传入存在默认群关系的 `hbOneNo`，应返回该默认群ID | `cm_wechat_group_user_rel` 中存在 `hbOneNo` 且 `is_default=1` 的关系记录 | `hbOneNo`: `HB001` | {"hbOneNo":"8012164854"} | 1. `Response.code` 为成功码（如 `0000`）。<br>2. `Response.data` 不为 `null`。<br>3. `Response.data.groupId` 与关系表一致。 |
| **TC-N-002** | 存在群关系但无默认群 | 传入存在群关系但未标记默认群的 `hbOneNo`，按实现可能返回某一“正常群”或业务定义的默认策略结果 | `cm_wechat_group_user_rel` 中存在 `hbOneNo` 多条记录但均 `is_default=0` | `hbOneNo`: `HB002` | {"hbOneNo":"8012164857"} | 1. `Response.code` 为成功码。<br>2. `Response.data` 可不为 `null`（取决于业务实现的默认群策略），若返回则 `groupId` 合理。 |
| **TC-N-003** | 无任何群关系 | 传入不存在任何群关系的 `hbOneNo` | 关系表中无该 `hbOneNo` 记录 | `hbOneNo`: `HB003` | {"hbOneNo":"8012947896"} | 1. `Response.code` 为成功码。<br>2. `Response.data` 为 `null`。 |

### 5.2. 异常及边界场景测试
| 用例ID | 用例标题 | 用例描述 | 前置条件 | 输入参数 | 输入参数json | 预期结果 |
| :--- | :--- | :--- | :--- | :--- | :--- | :--- |
| **TC-E-001** | `hbOneNo` 为 `null` | 传入 `null` 值 | 无 | `hbOneNo`: `null` | {"hbOneNo": null} | 1. 返回错误码（例如 `ResponseCodeEnum.SYS_ERROR` 或上游 `BusinessException` 被统一翻译的错误码）。<br>2. `Response.data` 为 `null`。 |
| **TC-E-002** | `hbOneNo` 为空串 | 传入空字符串 | 无 | `hbOneNo`: `""` | {"hbOneNo": ""} | 1. 返回错误码（例如 `ResponseCodeEnum.PARAM_ERROR`，实际取决于下游 `WechatGroupUserService` 的校验逻辑）。<br>2. `Response.data` 为 `null`。 |
| **TC-E-003** | `hbOneNo` 超长 | 传入超出业务允许长度的字符串 | 无 | `hbOneNo`: `长度>业务上限` | {"hbOneNo":"1234567890123456789012345678901234567890"} | 1. 返回错误码（如 `PARAM_ERROR`）。<br>2. `Response.data` 为 `null`。 |
| **TC-E-004** | 下游服务异常 | 下游 `WechatGroupUserService` 抛出 `BusinessException` 或系统异常 | 模拟/触发下游异常 | `hbOneNo`: `HB_ERR` | {"hbOneNo":"HB_ERR"} | 1. 返回错误码（如 `SYS_ERROR` 或统一异常码）。<br>2. `Response.description` 包含错误信息。 |
