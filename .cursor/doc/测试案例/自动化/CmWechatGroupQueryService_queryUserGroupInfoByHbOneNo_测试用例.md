# `CmWechatGroupQueryService.queryUserGroupInfoByHbOneNo` 接口测试用例

## 1. 接口概述

- **接口类型:** `Dubbo`
- **接口名称:** `CmWechatGroupQueryService.queryUserGroupInfoByHbOneNo`
- **接口路径:** `com.howbuy.crm.wechat.client.producer.cmwechatgroup.CmWechatGroupQueryService.queryUserGroupInfoByHbOneNo(hbOneNo)`
- **功能描述:** 根据一账通号查询该客户与微信群的关系信息列表，返回每个群的入群时间、退群时间（若有）及状态（已入群/已退群）。

## 2. 依赖数据表范围
为了给接口准备真实有效的测试数据，需要确保以下 **3个** 表中的数据是相互关联且逻辑正确的。

| 表名 | 用途 | 关键字段 | 关联逻辑 |
| :--- | :--- | :--- | :--- |
| `cm_wechat_cust_info` | 存放客户与企业微信外部联系人ID映射 | `HBONE_NO`, `EXTERNAL_USER_ID` | 通过 `HBONE_NO` 映射得到 `EXTERNAL_USER_ID` |
| `CM_WECHAT_GROUP_USER` | 存放客户在群内的成员关系与状态 | `CHAT_ID`, `EXTERNAL_USER_ID`, `USERCHATFLAG`, `JOIN_TIME`, `LEAVE_TIME` | 通过 `EXTERNAL_USER_ID` 查询该客户在各群的入退群情况 |
| `cm_wechat_group` | 存放群基础信息及状态 | `CHAT_ID`, `CHAT_FLAG`, `UPDATE_TIME` | 当成员记录 `LEAVE_TIME` 为空且群已解散(`CHAT_FLAG=1`)时，使用群的 `UPDATE_TIME` 作为退群时间 |

**数据准备核心思路:**
为目标 `HBONE_NO` 准备一条 `cm_wechat_cust_info` 记录以建立 `HBONE_NO -> EXTERNAL_USER_ID` 映射；在 `CM_WECHAT_GROUP_USER` 中准备若干条该外部联系人对应的群成员记录，覆盖在群(`USERCHATFLAG=0`)、已退群(`USERCHATFLAG!=0`，含有/没有 `LEAVE_TIME`)等情形；若需覆盖“群已解散但成员记录无退群时间”的分支，在 `cm_wechat_group` 中为对应 `CHAT_ID` 设置 `CHAT_FLAG=1` 且 `UPDATE_TIME` 不为空。

## 3. 输入参数 (`queryUserGroupInfoByHbOneNo(hbOneNo)`) 
| 参数名 | 类型 | 是否必填 | 描述 |
| :--- | :--- | :--- | :--- |
| `hbOneNo` | `String` | 是 | 一账通号，用于反查 `EXTERNAL_USER_ID` |

## 4. 输出结果 (`Response<List<UserGroupInfoVO>>`)
- **成功:** 返回 `Response.ok(List<UserGroupInfoVO>)`，其中 `UserGroupInfoVO` 字段包含：
  - `groupId`: 群ID (`CHAT_ID`)
  - `joinGroupTime`: 入群时间（yyyyMMddHHmmss）
  - `exitGroupTime`: 退群时间（yyyyMMddHHmmss），当记录存在 `LEAVE_TIME` 或群已解散且 `UPDATE_TIME` 不为空时返回
  - `status`: 加群状态（`2` 已入群，`3` 已退群）
- **失败:**
  - 当根据 `hbOneNo` 未查询到客户信息时，抛出业务异常并在实现层被转换为 `Response`：`code=C0510002`（参数错误），`description=根据一帐通未查询到客户信息`，`returnObject=null`。
  - 系统异常时返回：`code=C0510003`，`description=System error`，`returnObject=null`。

## 5. 测试用例

### 5.1. 正常场景测试
| 用例ID | 用例标题 | 用例描述 | 前置条件 | 输入参数 | 输入参数json | 预期结果 |
| :--- | :--- | :--- | :--- | :--- | :--- | :--- |
| **TC-N-001** | 在群-正常返回 | `HBONE_NO` 可映射到 `EXTERNAL_USER_ID`，`CM_WECHAT_GROUP_USER` 存在 `USERCHATFLAG=0` 的记录且 `JOIN_TIME` 不为空 | 表中已准备：`cm_wechat_cust_info(HBONE_NO, EXTERNAL_USER_ID)`；`CM_WECHAT_GROUP_USER(CHAT_ID, EXTERNAL_USER_ID, USERCHATFLAG=0, JOIN_TIME)` | `hbOneNo`: `有效值` | {"hbOneNo":"9000000011"} | 1. `Response.code=0000`。<br>2. `returnObject` 列表长度≥1。<br>3. 某条记录 `status=2`，`joinGroupTime` 不为空，`exitGroupTime` 为空。 |
| **TC-N-002** | 已退群-有退群时间 | 存在 `USERCHATFLAG!=0` 且 `LEAVE_TIME` 不为空的记录 | 表中已准备：`CM_WECHAT_GROUP_USER(..., USERCHATFLAG!=0, LEAVE_TIME!=NULL, JOIN_TIME!=NULL)` | `hbOneNo`: `有效值` | {"hbOneNo":"9200000103"} | 1. `Response.code=0000`。<br>2. 至少一条记录 `status=3`。<br>3. 该记录 `exitGroupTime` 等于 `LEAVE_TIME`（yyyyMMddHHmmss），`joinGroupTime` 不为空。 |
| **TC-N-003** | 已退群-无退群时间且群已解散 | 记录 `USERCHATFLAG!=0` 且 `LEAVE_TIME` 为空；对应群 `CHAT_FLAG=1` 且 `UPDATE_TIME` 不为空 | 表中已准备：`CM_WECHAT_GROUP_USER(..., USERCHATFLAG!=0, LEAVE_TIME=NULL)`；`cm_wechat_group(CHAT_ID, CHAT_FLAG=1, UPDATE_TIME!=NULL)` | `hbOneNo`: `有效值` | {"hbOneNo":"待准备数据"} | 1. `Response.code=0000`。<br>2. 至少一条记录 `status=3`。<br>3. 该记录 `exitGroupTime` 等于群 `UPDATE_TIME`（yyyyMMddHHmmss）。 |
| **TC-N-004** | 无任何群关系 | 可映射到 `EXTERNAL_USER_ID`，但无任何群成员记录 | `CM_WECHAT_GROUP_USER` 中无该 `EXTERNAL_USER_ID` 记录 | `hbOneNo`: `有效值` | {"hbOneNo":"8012947896"} | 1. `Response.code=0000`。<br>2. `returnObject` 为空列表。 |
| **TC-N-005** | 多群混合状态 | 同一客户存在多条群记录，覆盖在群与已退群两种状态 | 数据按需准备混合记录 | `hbOneNo`: `有效值` | {"hbOneNo":"待准备数据"} | 1. `Response.code=0000`。<br>2. 返回多条记录，`status` 能准确区分（在群=2；退群=3）。<br>3. 对于退群记录：若 `LEAVE_TIME` 为空且群解散，`exitGroupTime` 来源于群 `UPDATE_TIME`；否则来源于 `LEAVE_TIME` 或为空。 |

### 5.2. 异常及边界场景测试
| 用例ID | 用例标题 | 用例描述 | 前置条件 | 输入参数 | 输入参数json | 预期结果 |
| :--- | :--- | :--- | :--- | :--- | :--- | :--- |
| **TC-E-001** | 参数缺失-hbOneNo为空串 | 传入空字符串 | 无 | `hbOneNo`: `` | {"hbOneNo":""} | 1. `Response.code=C0510002`。<br>2. `Response.description` 包含`根据一帐通未查询到客户信息`。<br>3. `returnObject=null`。 |
| **TC-E-002** | 参数缺失-hbOneNo为null | 传入 `null` | 无 | `hbOneNo`: `null` | {"hbOneNo": null} | 1. `Response.code=C0510002`。<br>2. `Response.description` 包含`根据一帐通未查询到客户信息`。<br>3. `returnObject=null`。 |
| **TC-E-003** | 一账通不存在映射 | `cm_wechat_cust_info` 无该 `HBONE_NO` 记录 | 表中无映射 | `hbOneNo`: `无映射值` | {"hbOneNo":"HBONE_NO_NOT_EXISTS_999999"} | 1. `Response.code=C0510002`。<br>2. `Response.description` 包含`根据一帐通未查询到客户信息`。<br>3. `returnObject=null`。 |
| **TC-E-004** | 群已解散但无更新时间 | 存在退群记录且 `LEAVE_TIME` 为空，但 `cm_wechat_group.UPDATE_TIME` 也为空 | `cm_wechat_group` 中该 `CHAT_ID` 的 `CHAT_FLAG=1` 且 `UPDATE_TIME=NULL` | `hbOneNo`: `有效值` | {"hbOneNo":"待准备数据"} | 1. `Response.code=0000`。<br>2. 该退群记录 `exitGroupTime` 为空。 |
| **TC-E-005** | 系统异常 | 模拟底层DAO或Repository抛出未捕获异常 | 触发数据库或服务异常 | `hbOneNo`: `触发异常值` | {"hbOneNo":"HBONE_NO_TRIGGER_EXCEPTION"} | 1. `Response.code=C0510003`。<br>2. `Response.description=System error`。<br>3. `returnObject=null`。 |
