# `WechatCustInfoFacade` 接口测试用例

## 1. 接口概述

- **接口类型:** `Dubbo`
- **接口名称:** `WechatCustInfoFacade`
- **接口路径:** `com.howbuy.crm.wechat.client.facade.wechatcustinfo.WechatCustInfoFacade.queryWechatCustInfo(QueryWechatCustInfoRequest)`
- **功能描述:** 根据外部联系人ID或一账通号查询微信客户信息，返回客户头像、昵称、unionId、公司编码、外部联系人ID等。

## 2. 依赖数据表范围
为了给接口准备真实有效的测试数据，需要确保以下 **1个** 表中的数据是相互关联且逻辑正确的。

| 表名 | 用途 | 关键字段 | 关联逻辑 |
| :--- | :--- | :--- | :--- |
| `cm_wechat_cust_info` | 存储客户微信信息 | `ID`, `HBONE_NO`, `UNIONID`, `COMPANY_NO`, `EXTERNAL_USER_ID`, `NICK_NAME`, `wechat_avatar` | 查询时按 `EXTERNAL_USER_ID` 或 `HBONE_NO` + `COMPANY_NO` 过滤；`COMPANY_NO` 由 `WechatConfig.wealthCorpid` 通过 `CompanyWechatEnum` 映射得到 |

**数据准备核心思路:**
至少准备一条 `cm_wechat_cust_info` 有效记录，确保：`EXTERNAL_USER_ID`、`HBONE_NO` 非空；`COMPANY_NO` 与 `WechatConfig.wealthCorpid` 映射值一致；其余展示字段如 `NICK_NAME`、`UNIONID`、`wechat_avatar` 填充完整，以便断言返回字段。

## 3. 输入参数 (`QueryWechatCustInfoRequest`)
| 参数名 | 类型 | 是否必填 | 描述 |
| :--- | :--- | :--- | :--- |
| `externalUserId` | `String` | 否（与 `hboneNo` 二选一必填） | 外部联系人ID |
| `hboneNo` | `String` | 否（与 `externalUserId` 二选一必填） | 一账通号 |

## 4. 输出结果 (`WechatCustInfoVO`)
- **成功:** 返回 `Response.ok(Data)`，其中 `Response.code=0000`，`Data` 为 `WechatCustInfoVO`，包含：`hboneNo`、`unionid`、`nickName`、`companyNo`、`externalUserId`、`wechatAvatar`。当未查询到匹配数据时，`Response.code=0000` 且 `Response.returnObject=null`。
- **失败:** 当 `externalUserId` 与 `hboneNo` 同时为空时抛出业务异常（`BusinessException(ResponseCodeEnum.PARAM_ERROR)`，错误码 `C0510002`）。

## 5. 测试用例

### 5.1. 正常场景测试
| 用例ID | 用例标题 | 用例描述 | 前置条件 | 输入参数 | 输入参数json | 预期结果 |
| :--- | :--- | :--- | :--- | :--- | :--- | :--- |
| **TC-N-001** | 仅传externalUserId查询成功 | 提供存在的 `externalUserId`，按企业编码过滤后找到唯一记录 | `cm_wechat_cust_info` 中存在与 `externalUserId` 且公司编码匹配的记录 | `externalUserId`: `{存在的externalUserId}` | {"externalUserId":"wmyvNxCQAAv2QQldEV8qCXuwAgF4fOEw"} | 1. `Response.code`=`0000`。<br>2. `Response.returnObject` 不为 `null`。<br>3. 返回字段与库表记录一致（`hboneNo/unionid/nickName/companyNo/externalUserId/wechatAvatar`）。 |
| **TC-N-002** | 仅传hboneNo查询成功 | 提供存在的 `hboneNo`，按企业编码过滤后找到唯一记录 | `cm_wechat_cust_info` 中存在与 `hboneNo` 且公司编码匹配的记录 | `hboneNo`: `{存在的hboneNo}` | {"hboneNo":"9000000624"} | 1. `Response.code`=`0000`。<br>2. `Response.returnObject` 不为 `null`。<br>3. 字段正确性同上。 |
| **TC-N-003** | 同时传externalUserId与hboneNo查询成功 | 同时提供两个参数时以 `externalUserId` 分支查询 | `cm_wechat_cust_info` 中存在与 `externalUserId` 且公司编码匹配的记录 | `externalUserId`: `{存在的externalUserId}`, `hboneNo`: `{任意}` | {"externalUserId":"wml2gbEQAA3LdP80FaiMEIKZyAwon8tg","hboneNo":"9200000108"} | 1. `Response.code`=`0000`。<br>2. `Response.returnObject` 不为 `null`。<br>3. 断言返回结果来自 `externalUserId` 分支。 |

### 5.2. 异常及边界场景测试
| 用例ID | 用例标题 | 用例描述 | 前置条件 | 输入参数 | 输入参数json | 预期结果 |
| :--- | :--- | :--- | :--- | :--- | :--- | :--- |
| **TC-E-001** | 两参数均为空触发参数异常 | `externalUserId` 与 `hboneNo` 同时为空 | 无 | `externalUserId`: `null`, `hboneNo`: `null` | {"externalUserId": null, "hboneNo": null} | 抛出 `BusinessException`，错误码=`C0510002`（参数错误）。 |
| **TC-E-002** | externalUserId不存在 | 仅传 `externalUserId`，库表无匹配记录 | 无 | `externalUserId`: `{不存在的externalUserId}` | {"externalUserId":"not_exists_externalUserId_001"} | 1. `Response.code`=`0000`。<br>2. `Response.returnObject`=`null`。 |
| **TC-E-003** | hboneNo不存在 | 仅传 `hboneNo`，库表无匹配记录 | 无 | `hboneNo`: `{不存在的hboneNo}` | {"hboneNo":"999999999999"} | 1. `Response.code`=`0000`。<br>2. `Response.returnObject`=`null`。 |
| **TC-E-004** | 仅空字符串入参的边界 | 传入空字符串（长度为0）会被判定为空；两个参数皆为空字符串将触发参数异常 | 无 | `externalUserId`: `""`, `hboneNo`: `""` | {"externalUserId":"", "hboneNo":""} | 抛出 `BusinessException`，错误码=`C0510002`。 |

## 接口代码逻辑脑图
```plantuml
@startmindmap
* queryWechatCustInfo
** 参数校验
*** 条件：externalUserId 为空 且 hboneNo 为空
**** 反例：抛出 BusinessException(ResponseCodeEnum.PARAM_ERROR) 错误码 C0510002
** 查询分支
*** externalUserId 非空
**** 调用：getWechatCustByExternalUserId(externalUserId, WechatConfig.wealthCorpid)
*** 否则（仅 hboneNo 非空）
**** 调用：getExternalUserByHboneNo(hboneNo, WechatConfig.wealthCorpid)
** 结果处理
*** 未查询到（PO 为 null）
**** 返回 null
*** 查询到记录
**** 属性映射为 WechatCustInfoVO 并返回
** 边界与优先级
*** 同时传 externalUserId 与 hboneNo
**** externalUserId 分支优先
*** 空值判定
**** 使用 StringUtils.isEmpty：null 或 "" 视为为空（空格不视为空）
** 异常与外部依赖
*** DAO/配置异常
**** 未捕获，向上抛出（交由全局异常机制处理）
@endmindmap
```