# `QueryWeChatMemberInfoService.queryWeChatMemberInfo` 接口测试用例

## 1. 接口概述

- **接口类型:** `Dubbo`
- **接口名称:** `QueryWeChatMemberInfoService.queryWeChatMemberInfo`
- **接口路径:** `com.howbuy.crm.wechat.client.producer.wechatmembermanagement.QueryWeChatMemberInfoService.queryWeChatMemberInfo(QueryWeChatMemberInfoRequest)`
- **功能描述:** 根据企业微信成员 `userId` 查询成员基础信息，支持返回二维码与头像合成后的 Base64 字符串。

## 2. 依赖数据表范围
为了给接口准备真实有效的测试数据，需要确保以下 **0个** 表中的数据是相互关联且逻辑正确的。

| 表名 | 用途 | 关键字段 | 关联逻辑 |
| :--- | :--- | :--- | :--- |
| `无` | 接口仅依赖企业微信开放平台返回结果 | `-` | `-` |

**数据准备核心思路:**
本接口不直接依赖数据库数据。测试时可通过 Mock 框架模拟企业微信接口返回结果（如 `errcode`、`errmsg` 及成员字段）。

## 3. 输入参数 (`QueryWeChatMemberInfoRequest`)
| 参数名 | 类型 | 是否必填 | 描述 |
| :--- | :--- | :--- | :--- |
| `userId` | `String` | 是 | 企业微信成员id |
| `needQrImageStr` | `Boolean` | 否 | 是否需要返回二维码与头像合成后的 Base64 字符串，默认 `false` |

## 4. 输出结果 (`Response<QueryWeChatMemberInfoResponse>`)
- **成功:** 返回 `Response.ok(Data)`，其中 `Data` 为 `QueryWeChatMemberInfoResponse`，包含：
  - `userId`：成员ID
  - `name`：成员姓名
  - `email`：成员邮箱
  - `qrCode`：员工个人二维码（扫描可添加为外部联系人）
  - `mobile`：手机号
  - `thumbAvatar`：头像缩略图URL
  - `enterpriseWechatQrImageStr`：当 `needQrImageStr=true` 时返回，值以 `data:image/png;base64,` 为前缀
- **失败:** 当上游返回错误码（`errcode` 非 `0`）时，返回 `Response.code=C0519999`（UNKNOWN_ERROR），`Response.description` 为上游返回的 `errmsg`，`Response.returnObject=null`。
- **特殊情况:** 当查询结果为空（上游返回 `null`）时，返回 `Response.code=0000`，`Response.description=没有查询到数据`，`Response.returnObject=null`。

## 5. 测试用例

### 5.1. 正常场景测试
| 用例ID | 用例标题 | 用例描述 | 前置条件 | 输入参数 | 输入参数json | 预期结果 |
| :--- | :--- | :--- | :--- | :--- | :--- | :--- |
| **TC-N-001** | 成员信息查询（不生成合成图） | 传入有效 `userId`，`needQrImageStr=false`，上游返回 `errcode=0` 且包含完整字段 | Mock 上游返回：`errcode=0`,`errmsg=ok`，并包含 `userid/name/email/qr_code/mobile/thumb_avatar` | `userId`: `U_001`；`needQrImageStr`: `false` | {"userId":"kala.zhang","needQrImageStr":false} | 1. `Response.code=0000`。<br>2. `Response.description=ok`。<br>3. `Response.returnObject` 不为 `null`，且包含 `userId/name/email/qrCode/mobile/thumbAvatar`。<br>4. `enterpriseWechatQrImageStr` 为空或不存在。 |
| **TC-N-002** | 成员信息查询（生成合成图） | 传入有效 `userId`，`needQrImageStr=true`，上游返回 `errcode=0` | Mock 上游返回：`errcode=0`,`errmsg=ok`，并包含 `qr_code` 与 `thumb_avatar` 可用 | `userId`: `U_002`；`needQrImageStr`: `true` | {"userId":"xiaojuan.wang","needQrImageStr":true} | 1. `Response.code=0000`。<br>2. `Response.description=ok`。<br>3. `Response.returnObject.enterpriseWechatQrImageStr` 以 `data:image/png;base64,` 为前缀。 |

### 5.2. 异常及边界场景测试
| 用例ID | 用例标题 | 用例描述 | 前置条件 | 输入参数 | 输入参数json | 预期结果 |
| :--- | :--- | :--- | :--- | :--- | :--- | :--- |
| **TC-E-001** | 上游返回错误码 | 上游企业微信接口返回 `errcode!=0`（如：`40014`） | Mock 上游返回：`errcode=40014`,`errmsg=invalid access_token` | `userId`: `U_ERR`；`needQrImageStr`: `false` | {"userId":"jun.tan","needQrImageStr":false} | 1. `Response.code=C0519999`。<br>2. `Response.description` 包含 `invalid access_token`。<br>3. `Response.returnObject=null`。 |
| **TC-E-002** | 未查询到数据 | 上游返回 `null` | Mock 上游返回：`null` | `userId`: `U_NOT_EXIST`；`needQrImageStr`: `false` | {"userId":"NOT_EXIST_999999","needQrImageStr":false} | 1. `Response.code=0000`。<br>2. `Response.description=没有查询到数据`。<br>3. `Response.returnObject=null`。 |
| **TC-E-003** | 请求对象为 `null` | 直接传入 `null` 请求对象 | 无 | `request`: `null` | null | 1. 调用抛出异常（如 `NullPointerException`），由框架统一处理。 |
| **TC-E-004** | 合成图生成异常 | `needQrImageStr=true` 时，二维码或头像资源异常导致合成失败 | Mock `QrCodeLogoUtil.transferToBase64` 抛出异常 | `userId`: `U_003`；`needQrImageStr`: `true` | {"userId":"ling.ma","needQrImageStr":true} | 1. `Response.code=0000`。<br>2. `Response.description=ok`。<br>3. `Response.returnObject.enterpriseWechatQrImageStr` 以 `data:image/png;base64,` 为前缀（Base64 体可能为空）。 |


