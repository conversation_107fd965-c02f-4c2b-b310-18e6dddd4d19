# `CmWechatGroupQueryService.getGroupBreakStatus` 接口测试用例

## 1. 接口概述

- **接口类型:** `Dubbo`
- **接口名称:** `CmWechatGroupQueryService.getGroupBreakStatus`
- **接口路径:** `com.howbuy.crm.wechat.client.producer.cmwechatgroup.CmWechatGroupQueryService.getGroupBreakStatus(String)`
- **功能描述:** 根据微信群 `groupId` 查询该群是否已解散，并返回解散状态信息。

## 2. 依赖数据表范围
为了给接口准备真实有效的测试数据，需要确保以下 **1个** 表中的数据是相互关联且逻辑正确的。

| 表名 | 用途 | 关键字段 | 关联逻辑 |
| :--- | :--- | :--- | :--- |
| `cm_wechat_group` | 存储微信群基础信息及状态 | `CHAT_ID`, `CHAT_FLAG`, `UPDATE_TIME` | 通过 `CHAT_ID` 根据入参 `groupId` 查询；`CHAT_FLAG` 为群状态标识（0-正常，1-解散）。|

**数据准备核心思路:**
至少准备两条 `cm_wechat_group` 记录，用于覆盖未解散（`CHAT_FLAG=0`）与已解散（`CHAT_FLAG=1`）两种状态；另准备一条不存在于表中的 `groupId` 以覆盖“未查询到数据”。如需验证解散时间显示，可在业务允许范围内准备 `UPDATE_TIME` 字段，但当前实现仅返回 `groupBreakStatus`。

## 3. 输入参数 (`groupId`)
| 参数名 | 类型 | 是否必填 | 描述 |
| :--- | :--- | :--- | :--- |
| `groupId` | `String` | 是 | 微信群ID，作为唯一业务标识，用于定位群状态 |

## 4. 输出结果 (`Response<GroupBreakStatusVO>`)
- **成功:** 返回 `Response.ok(Data)`，其中 `Data` 为 `GroupBreakStatusVO`，字段包括：`groupBreakStatus`（是否解散：0-否 1-是）。当前实现未赋值 `breakGroupTime`，可能为 `null`。
- **失败:**
  - 入参为空：返回参数错误码（如 `ResponseCodeEnum.PARAM_ERROR`）。
  - 未查询到数据：返回未查询到数据错误码（如 `ResponseCodeEnum.NULL_ERROR`）。
  - 系统异常：返回系统错误码（如 `ResponseCodeEnum.SYS_ERROR`）。

## 5. 测试用例

### 5.1. 正常场景测试
| 用例ID | 用例标题 | 用例描述 | 前置条件 | 输入参数 | 输入参数json | 预期结果 |
| :--- | :--- | :--- | :--- | :--- | :--- | :--- |
| **TC-N-001** | 群未解散 | 传入存在的 `groupId` 且 `CHAT_FLAG=0` | `cm_wechat_group` 中存在 `groupId` 对应记录，`CHAT_FLAG=0` | `groupId`: `G_NORMAL` | {"groupId":"wrl2gbEQAAfX8kufAsa3FaBJ3AKC84XQ"} | 1. `Response.code` 为成功码（如 `0000`）。<br>2. `Response.data` 不为 `null`。<br>3. `Response.data.groupBreakStatus` 为 `0`。 |
| **TC-N-002** | 群已解散 | 传入存在的 `groupId` 且 `CHAT_FLAG=1` | `cm_wechat_group` 中存在 `groupId` 对应记录，`CHAT_FLAG=1` | `groupId`: `G_BROKEN` | {"groupId":"CWX000001"} | 1. `Response.code` 为成功码（如 `0000`）。<br>2. `Response.data` 不为 `null`。<br>3. `Response.data.groupBreakStatus` 为 `1`。 |

### 5.2. 异常及边界场景测试
| 用例ID | 用例标题 | 用例描述 | 前置条件 | 输入参数 | 输入参数json | 预期结果 |
| :--- | :--- | :--- | :--- | :--- | :--- | :--- |
| **TC-E-001** | `groupId` 为 `null` | 传入 `null` 值 | 无 | `groupId`: `null` | {"groupId": null} | 1. 返回参数错误码（如 `ResponseCodeEnum.PARAM_ERROR`）。<br>2. `Response.data` 为 `null`。 |
| **TC-E-002** | `groupId` 为空串 | 传入空字符串 | 无 | `groupId`: `""` | {"groupId": ""} | 1. 返回参数错误码（如 `ResponseCodeEnum.PARAM_ERROR`）。<br>2. `Response.data` 为 `null`。 |
| **TC-E-003** | 未查询到数据 | 传入不存在于 `cm_wechat_group` 的 `groupId` | 表中无该 `groupId` 记录 | `groupId`: `G_NOT_EXIST` | {"groupId": "G_NOT_EXIST_999999"} | 1. 返回未查询到数据错误码（如 `ResponseCodeEnum.NULL_ERROR`）。<br>2. `Response.data` 为 `null`。 |
| **TC-E-004** | 系统异常 | 模拟底层数据访问异常 | 触发数据库或服务异常 | `groupId`: `G_SYS_ERR` | {"groupId":"wrl2gbEQAAfX8kufAsa3FaBJ3AKC84XQ"} | 1. 返回系统错误码（如 `ResponseCodeEnum.SYS_ERROR`）。<br>2. `Response.description` 包含错误信息或通用系统错误提示。 |
