/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.client.enums;

/**
 * @description: 微信应用枚举
 * <AUTHOR>
 * @date 2024/9/19 15:29
 * @since JDK 1.8
 */
@Deprecated
public enum WechatAppEnum {

    /**
     * [好买财富]企业微信-客户联系
     * 同 {@link com.howbuy.crm.wechat.client.enums.WechatApplicationEnum}
     */

    WEALTH_CUSTOMER("wealth_customer"),

    /**
     * [好买财富]企业微信-自建应用-CRM
     */
    WEALTH_CRM("wealth_crm"),

    /**
     * [好买财富]企业微信-自建应用-商路通
     */
    WEALTH_RHSLT("wealth_rhslt"),

    /**
     * [好买财富]企业微信-自建应用-画像
     */
    WEALTH_PORTRAIT("wealth_portrait"),

    /**
     * [好买基金]企业微信-客户联系
     */
    FUND_CUSTOMER("fund_customer"),

    /**
     * [好买财富]企业微信-机构服务通知
     */
    ORG_SERVICE_NOTICE("org_service_notice"),

    ;

    private String key;

    WechatAppEnum(String key) {
        this.key = key;
    }

    public String getKey() {
        return key;
    }

}
