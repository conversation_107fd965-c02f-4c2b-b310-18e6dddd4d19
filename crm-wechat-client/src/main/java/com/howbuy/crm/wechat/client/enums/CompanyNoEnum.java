/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.client.enums;

/**
 * @description: (企微  企业主体：  1-好买财富  2-好买基金)
 * <AUTHOR>
 * @date 2025/7/11 11:07
 * @since JDK 1.8
 */
public enum CompanyNoEnum {
    /**
     * 1-好买财富
     */
    HOWBUY_WEALTH("1", "好买财富"),

    /**
     * 2-好买基金
     */
    HOWBUY_FUND("2", "好买基金"),


    /**
     * 3-好晓买
     */
    HOWBUY_HXM("3", "好晓买"),

    ;

    /**
     * 编码
     */
    private String code;


    /**
     * 描述
     */
    private String description;

    CompanyNoEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 通过code获得
     *
     * @param code 系统返回参数编码
     * @return description 描述
     */
    public static String getDescription(String code) {
        CompanyNoEnum statusEnum = getEnum(code);
        return statusEnum == null ? null : statusEnum.getDescription();
    }


    /**
     * 通过code直接返回 整个枚举类型
     *
     * @param code 系统返回参数编码
     * @return BaseConstantEnum
     */
    public static CompanyNoEnum getEnum(String code) {
        for (CompanyNoEnum statusEnum : CompanyNoEnum.values()) {
            if (statusEnum.getCode().equals(code)) {
                return statusEnum;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}