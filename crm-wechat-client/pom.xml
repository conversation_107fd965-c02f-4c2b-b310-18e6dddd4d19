<?xml version='1.0' encoding='utf-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>crm-wechat</artifactId>
        <groupId>com.howbuy.crm</groupId>
        <version>2.0.3.3-RELEASE</version>
    </parent>
    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>

        <dependency>
            <artifactId>howbuy-commons-validator</artifactId>
            <groupId>com.howbuy.commons.validator</groupId>
        </dependency>

    </dependencies>
    <modelVersion>4.0.0</modelVersion>

    <name>crm-wechat-client</name>
    <artifactId>crm-wechat-client</artifactId>


    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
    </properties>


</project>