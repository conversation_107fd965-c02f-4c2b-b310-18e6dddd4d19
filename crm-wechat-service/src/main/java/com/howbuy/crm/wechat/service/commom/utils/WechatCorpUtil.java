/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.commom.utils;

import com.howbuy.crm.wechat.client.enums.CompanyNoEnum;
import com.howbuy.crm.wechat.client.enums.WechatApplicationEnum;
import com.howbuy.crm.wechat.service.service.config.ApplicationConfigManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @description: (企业微信-企业主体-映射关系)
 * <AUTHOR>
 * @date 2025/7/11 13:12
 * @since JDK 1.8
 */
@Slf4j
@Component
public class WechatCorpUtil {

    @Autowired
    private ApplicationConfigManager applicationConfigManager;



    /**
     * 根据企业编码获取发送消息的应用配置
     * @param companyNo 企业编码
     * @return 发送消息的应用配置
     */
    public ApplicationUtilityDTO getSendMsgApp(String companyNo) {
        return applicationConfigManager.getSendMsgApplication(companyNo);
    }

    /**
     * 根据公司编号获取发送消息的微信应用枚举（静态方法，保持向后兼容）
     * @param companyNoEnum 企微-企业主体
     * @return 微信应用枚举
     * @deprecated 请使用基于Spring Bean的方法
     */
    @Deprecated
    public static WechatApplicationEnum getSendMsgAppEnum(CompanyNoEnum companyNoEnum) {
        switch (companyNoEnum) {
            case HOWBUY_WEALTH:
                return WechatApplicationEnum.WEALTH_CRM;
            case HOWBUY_FUND:
                // 企业默认发送消息的内建应用，如需要，请自行添加
                return null;
            default:
                return null;
        }
    }

}