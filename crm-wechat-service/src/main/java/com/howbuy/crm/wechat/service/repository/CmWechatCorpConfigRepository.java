/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.repository;

import com.howbuy.crm.wechat.dao.mapper.CmWechatApplicationMapper;
import com.howbuy.crm.wechat.dao.mapper.CmWechatCompanyMapper;
import com.howbuy.crm.wechat.dao.po.CmWechatApplicationPO;
import com.howbuy.crm.wechat.dao.po.CmWechatCompanyPO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * @description: (企微配置 repository 类)
 * <AUTHOR>
 * @date 2025/7/24 9:16
 * @since JDK 1.8
 */
@Repository
@Transactional(propagation = Propagation.SUPPORTS, rollbackFor = Exception.class)
public class CmWechatCorpConfigRepository {

    @Autowired
    private CmWechatCompanyMapper cmWechatCompanyMapper;

    @Autowired
    private CmWechatApplicationMapper cmWechatApplicationMapper;



    /**
     * 查询有效的公司配置信息列表
     * @return List<CmWechatCompanyPO>
     */
    public List<CmWechatCompanyPO> selectCorpConfigList(){
        return cmWechatCompanyMapper.selectConfigList();
    }


    /**
     * 查询有效的应用配置信息列表
     * @return List<CmWechatApplicationPO>
     */
    public List<CmWechatApplicationPO> selectApplicationConfigList(){
        return cmWechatApplicationMapper.selectApplicationConfigList();
    }



    /**
     * 根据companyNo查询有效的公司配置信息
     * @param companyNo
     * @return
     */
    public CmWechatCompanyPO getCorpInfoByCompanyNo(String companyNo) {
        return cmWechatCompanyMapper.selectConfigByCompanyNo(companyNo);
    }


    /**
     * 根据corpId查询有效的公司配置信息
     * @param corpId
     * @return
     */
    public CmWechatCompanyPO getCorpInfoByCorpId(String corpId) {
        return cmWechatCompanyMapper.selectConfigByCorpId(corpId);
    }


    /**
     * 根据applicationCode查询有效的应用配置信息
     * @param applicationCode
     * @return
     */
    public CmWechatApplicationPO getApplicationInfo(String applicationCode) {
        return cmWechatApplicationMapper.selectConfigByApplicationCode(applicationCode);
    }

}