package com.howbuy.crm.wechat.service.outerservice.wechatapi.sendwechat;

import cn.hutool.http.HttpUtil;
import com.howbuy.crm.wechat.client.enums.WechatApplicationEnum;
import com.howbuy.crm.wechat.service.commom.constant.Constants;
import com.howbuy.crm.wechat.service.commom.utils.ApplicationUtilityDTO;
import com.howbuy.crm.wechat.service.commom.utils.WechatCorpUtil;
import com.howbuy.crm.wechat.service.outerservice.wechatapi.WeChatCommonOuterService;
import com.howbuy.crm.wechat.service.service.config.BaseConfigServce;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Map;

/**
 * @classname: SendMsgServer
 * @author: yu.zhang
 * @description: 发送企业微信消息接口
 * @creatdate: 2021-02-10 11:00
 * @since: JDK1.8
 */
@Slf4j
@Service
@Transactional
public class WechatMessageSendOuterServer {

    @Autowired
    private WeChatCommonOuterService weChatCommonOuterService;

    @Autowired
    private BaseConfigServce baseConfigServce;

    /**
     * @description:(请在此添加描述)
     * @param paramMap 消息体
     * @param applicationEnum 发送消息的内建应用
     * @return void
     * @author: yu.zhang
     * @date: 2023/6/27 14:59
     * @since JDK 1.8
     */
    public void sendMsgByType(Map<String, Object> paramMap, WechatApplicationEnum applicationEnum) {
        log.info("发送消息sendMsgByType开始:" + paramMap);
        try {

            ApplicationUtilityDTO  utilityDTO= baseConfigServce.getApplicationUtilityDTO(applicationEnum);

            paramMap.put("agentid", utilityDTO.getCorpId());

            paramMap.put("enable_duplicate_check", "0");
            paramMap.put("duplicate_check_interval", "30");

            String token = weChatCommonOuterService.getCacheTokenByAppEnum(applicationEnum);

            StringBuilder sendUrl = new StringBuilder();
            sendUrl.append(Constants.INTERACT_DOMAIN_URL).append(String.format(Constants.MSG_PATH, token));

            String resp = HttpUtil.post(sendUrl.toString(), paramMap);
            log.info("获取到的token:{},请求数据:{},发送微信的响应数据:{}", token, paramMap, resp);
        } catch (Exception e) {
            log.error("发送消息sendMsgByType Exception:{}", e.getMessage());
        }
    }

}
