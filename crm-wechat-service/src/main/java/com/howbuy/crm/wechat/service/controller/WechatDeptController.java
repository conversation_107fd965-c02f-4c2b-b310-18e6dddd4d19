package com.howbuy.crm.wechat.service.controller;

import com.howbuy.crm.wechat.client.enums.CompanyNoEnum;
import com.howbuy.crm.wechat.service.commom.utils.CompanyNoUtils;
import com.howbuy.crm.wechat.service.service.WechatFullDeptDataScheduleService;
import crm.howbuy.base.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Created by shucheng on 2022/1/19 11:12
 */
@Slf4j
@Controller
@RequestMapping("/wechatdept")
public class WechatDeptController {

    @Autowired
    private WechatFullDeptDataScheduleService wechatFullDeptDataScheduleService;


    /**
     * @api {GET} /wechatdept/execute executeScheduleService()
     * @apiVersion 1.0.0
     * @apiGroup WechatDeptController
     * @apiName executeScheduleService()
     * @apiDescription 手动执行任务（产线使用compareInsertAll）
     * @apiSuccess (响应结果) {String} response
     * @apiSuccessExample 响应结果示例
     * "jVOxpVGaVl"
     */
    @ResponseBody
    @GetMapping("/execute")
    public String executeScheduleService(String companyNos) {

        //companyNos ： 1,2
        if(StringUtil.isNotNullStr(companyNos)){
            // 临时转换为CompanyNoEnum以保持业务逻辑不变，后续WechatFullDeptDataScheduleService也需要重构
            List<String> companyNoList = Arrays.stream(companyNos.split(","))
                    .map(String::trim)
                    .filter(companyNo -> CompanyNoUtils.isValidEnumValue(companyNo))
                    .collect(Collectors.toList());

            if (!companyNoList.isEmpty()) {
                // 转换为CompanyNoEnum列表（临时方案）
                List<CompanyNoEnum> companyNoEnumList = companyNoList.stream()
                        .map(CompanyNoUtils::toCompanyNoEnum)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());
                wechatFullDeptDataScheduleService.executeSync(companyNoEnumList);
            }
        }

        return "success";
    }
}
