/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.service.wechatusertag;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.howbuy.crm.wechat.client.base.BaseCompanyNoRequest;
import com.howbuy.crm.wechat.client.base.Response;
import com.howbuy.crm.wechat.client.enums.CompanyNoEnum;
import com.howbuy.crm.wechat.client.enums.ResponseCodeEnum;
import com.howbuy.crm.wechat.client.producer.wechatusertag.request.AddUserTagRequest;
import com.howbuy.crm.wechat.client.producer.wechatusertag.request.DeleteUserTagRequest;
import com.howbuy.crm.wechat.dao.po.CmWechatCustInfoPO;
import com.howbuy.crm.wechat.dao.po.CmWechatCustRelationPO;
import com.howbuy.crm.wechat.service.commom.utils.CompanyNoUtils;
import com.howbuy.crm.wechat.service.outerservice.wechatapi.WechatExternalContactOuterService;
import com.howbuy.crm.wechat.service.repository.CmWechatCustInfoRepository;
import com.howbuy.crm.wechat.service.repository.CmWechatCustRelationRepository;
import crm.howbuy.base.constants.StaticVar;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * @description: 企业微信用户标签服务类
 * <AUTHOR>
 * @date 2024/6/12 16:42
 * @since JDK 1.8
 */
@Service
@Slf4j
public class UserTagService {

    @Autowired
    private CmWechatCustInfoRepository cmWechatCustInfoRepository;

    @Autowired
    private CmWechatCustRelationRepository cmWechatCustRelationRepository;

    @Autowired
    private WechatExternalContactOuterService wechatExternalContactOuterService;



    /**
     * 获取企业编码，支持默认值
     * @param request 请求对象
     * @return 企业编码
     */
    private static String getCompanyNo(BaseCompanyNoRequest request) {
        return CompanyNoUtils.getCompanyNo(request);
    }

    /**
     * 兼容历史逻辑。 如果companyNo参数为空。赋值：HOWBUY_WEALTH-好买财富
     * @param request 请求对象
     * @return 企业编码枚举
     * @deprecated 请使用 {@link #getCompanyNo(BaseCompanyNoRequest)} 方法
     */
    @Deprecated
    private static CompanyNoEnum getCompanyNoEnum(BaseCompanyNoRequest request) {
        return CompanyNoUtils.toCompanyNoEnum(request.getCompanyNo());
    }


    /**
     * @description: 添加企业微信外部客户标签
     * @param request
     * @return com.howbuy.crm.wechat.client.base.Response<java.lang.String>
     * @author: hongdong.xie
     * @date: 2024/6/13 09:22
     * @since JDK 1.8
     */
    public Response<String> add(AddUserTagRequest request) {
        log.info("add request:{}",JSON.toJSONString(request));
        //获取 企业 编号
        CompanyNoEnum companyNoEnum=getCompanyNoEnum(request);
        Pair<String,String> result = markTagUsers(request.getHboneNo(), request.getTagIdList(),null, companyNoEnum);
        Response<String> response =  processUserTagReuslt(result);
        log.info("add response:{}",JSON.toJSONString(response));

        return response;
    }

    /**
     * @description: 删除企业微信外部客户标签
     * @param request
     * @return com.howbuy.crm.wechat.client.base.Response<java.lang.String>
     * @author: hongdong.xie
     * @date: 2024/6/13 09:22
     * @since JDK 1.8
     */
    public Response<String> delete(DeleteUserTagRequest request) {
        log.info("delete request:{}",JSON.toJSONString(request));

        CompanyNoEnum companyNoEnum=getCompanyNoEnum(request);

        Pair<String,String> result = markTagUsers(request.getHboneNo(), null,request.getTagIdList(),companyNoEnum);
        Response<String> response = processUserTagReuslt(result);
        log.info("delete response:{}",JSON.toJSONString(response));
        return response;
    }


    /**
     * @description: 编辑企业外部客户标签
     * @param hboneNo 一账通号
     * @param addTagList 新增的标签列表
     * @param removeTagList 删除的标签列表
     * @param companyNoEnum 企微-企业主体
     * @return org.apache.commons.lang3.tuple.Pair,左边是返回码，右边是返回描述
     * @author: hongdong.xie
     * @date: 2024/1/27 11:08
     * @since JDK 1.8
     */
    private Pair<String,String> markTagUsers(String hboneNo, List<String> addTagList, List<String> removeTagList, CompanyNoEnum companyNoEnum) {
        if(CollectionUtils.isEmpty(addTagList) && CollectionUtils.isEmpty(removeTagList)){
            log.info("addTagList和removeTagList都为空，不执行标签操作");
            return null;
        }

        if (StringUtils.isEmpty(hboneNo)) {
            log.info("hboneNo为空，不执行标签操作");
            return null;
        }

        // 根据一账通号获取客户企业微信ID
        CmWechatCustInfoPO custInfoPO = cmWechatCustInfoRepository.getExternalUserByHboneNo(hboneNo,companyNoEnum);
        if (Objects.isNull(custInfoPO) || StringUtils.isEmpty(custInfoPO.getExternalUserId())) {
            log.info("根据一账通号：{}，获取不到客户企业微信ID，不执行标签操作", hboneNo);
            return Pair.of(ResponseCodeEnum.NULL_ERROR.getCode(),ResponseCodeEnum.NULL_ERROR.getDescription());
        }

        String externalUserId = custInfoPO.getExternalUserId();
        List<CmWechatCustRelationPO> relationList =
                cmWechatCustRelationRepository.getRealtionListByExternalUserId(companyNoEnum,
                                                                              Lists.newArrayList(externalUserId));
        if (CollectionUtils.isEmpty(relationList)) {
            log.info("根据外部客户企业微信ID：{}，获取不到客户投顾关联关系，不执行标签操作", externalUserId);
            return Pair.of(ResponseCodeEnum.NULL_ERROR.getCode(),ResponseCodeEnum.NULL_ERROR.getDescription());
        }

        String userId = relationList.get(0).getConscode();
        return wechatExternalContactOuterService.markTagUsers(userId, externalUserId, addTagList, removeTagList, companyNoEnum);
    }

    /**
     * @description: 处理用户标签返回结果
     * @param result 左边是返回码，右边是返回描述
     * @return com.howbuy.crm.common.Response<java.lang.String>
     * @author: hongdong.xie
     * @date: 2024/1/27 13:32
     * @since JDK 1.8
     */
    private Response<String> processUserTagReuslt(Pair<String, String> result) {
        if (result == null) {
            return new Response<>(ResponseCodeEnum.PARAM_ERROR.getCode(), ResponseCodeEnum.PARAM_ERROR.getDescription(), "");
        }

        String errorCode = result.getLeft();
        String errorMsg = result.getRight();
        if(Objects.equals(ResponseCodeEnum.NULL_ERROR.getCode(), errorCode)){
            return new Response<>(errorCode, errorMsg, "");
        }
        if(StaticVar.WECHAT_RESPONSE_SUCCESS.equals(errorCode)){
            return Response.ok("");
        }
        return new Response<>(ResponseCodeEnum.UNKNOWN_ERROR.getCode(), errorMsg, "");
    }
}