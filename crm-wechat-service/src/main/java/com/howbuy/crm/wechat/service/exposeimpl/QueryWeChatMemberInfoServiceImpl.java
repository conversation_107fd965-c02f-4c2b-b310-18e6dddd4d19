/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.exposeimpl;

import com.howbuy.crm.wechat.client.base.Response;
import com.howbuy.crm.wechat.client.enums.CompanyNoEnum;
import com.howbuy.crm.wechat.client.enums.ResponseCodeEnum;
import com.howbuy.crm.wechat.client.producer.wechatmembermanagement.QueryWeChatMemberInfoService;
import com.howbuy.crm.wechat.client.producer.wechatmembermanagement.request.QueryWeChatMemberInfoRequest;
import com.howbuy.crm.wechat.client.producer.wechatmembermanagement.response.QueryWeChatMemberInfoResponse;
import com.howbuy.crm.wechat.service.commom.utils.CompanyNoUtils;
import com.howbuy.crm.wechat.service.commom.utils.QrCodeLogoUtil;
import com.howbuy.crm.wechat.service.outerservice.wechatapi.domain.ExternalUserInfoDTO;
import com.howbuy.crm.wechat.service.service.wechatmembermanagement.WeChatMemberInfoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/6/20 13:27
 * @since JDK 1.8
 */
@Slf4j
@DubboService
public class QueryWeChatMemberInfoServiceImpl implements QueryWeChatMemberInfoService {

    /**
     * base64图片前缀
     */
    private static final String BASE64_PREFIX = "data:image/png;base64,";

    @Resource
    private WeChatMemberInfoService weChatMemberInfoService;

    @Override
    public Response<QueryWeChatMemberInfoResponse> queryWeChatMemberInfo(QueryWeChatMemberInfoRequest request) {
        // 使用CompanyNoUtils处理企业编码
        String companyNo = CompanyNoUtils.getCompanyNo(request.getCompanyNo());
        CompanyNoEnum companyNoEnum = CompanyNoUtils.toCompanyNoEnum(companyNo);
        // 参数校验
        ExternalUserInfoDTO userInfoDTO = weChatMemberInfoService.queryWeChatMemberInfo(companyNoEnum,request.getUserId());
        if(null == userInfoDTO){
            log.info("queryWeChatMemberInfo >>> 查询微信成员信息失败，userId:{}", request.getUserId());
            return new Response<>(ResponseCodeEnum.SUCCESS.getCode(), "没有查询到数据", null);
        }
        //结果判断
        if(!userInfoDTO.isSuccess()){
            log.info("queryWeChatMemberInfo >>> 查询微信成员信息错误，userId:{},errcode:{},errmsg:{}",
                    request.getUserId(), userInfoDTO.getErrcode(), userInfoDTO.getErrmsg());
            return new Response<>(ResponseCodeEnum.UNKNOWN_ERROR.getCode(), userInfoDTO.getErrmsg(), null);
        }
        QueryWeChatMemberInfoResponse chatMemberInfoResponse = new QueryWeChatMemberInfoResponse();
        chatMemberInfoResponse.setUserId(userInfoDTO.getUserid());
        chatMemberInfoResponse.setName(userInfoDTO.getName());
        chatMemberInfoResponse.setEmail(userInfoDTO.getEmail());
        chatMemberInfoResponse.setQrCode(userInfoDTO.getQrCode());
        chatMemberInfoResponse.setThumbAvatar(userInfoDTO.getThumbAvatar());
        chatMemberInfoResponse.setMobile(userInfoDTO.getMobile());

        if (request.getNeedQrImageStr()) {
            // 将企微头像放置在企微二维码的中间，并把图片转成base64字符串
            String base64 = "";
            try {
                base64 = QrCodeLogoUtil.transferToBase64(userInfoDTO.getQrCode(),
                        userInfoDTO.getThumbAvatar());
            } catch (Exception e) {
                log.error("queryWeChatMemberInfo >>> 生成企微二维码失败", e);
            }
            chatMemberInfoResponse.setEnterpriseWechatQrImageStr(BASE64_PREFIX + base64);
        }
        if (request.getNeedQwAddressBase64()) {
            // 将企微头像放置在企微二维码的中间，并把图片转成base64字符串
            String base64 = "";
            try {
                base64 = QrCodeLogoUtil.transferToBase64(userInfoDTO.getQrCode());
            } catch (Exception e) {
                log.error("queryWeChatMemberInfo >>> 生成企微二维码base64数据失败", e);
            }
            chatMemberInfoResponse.setEnterpriseWechatQrNoImageStr(base64);
        }

        return new Response<>(ResponseCodeEnum.SUCCESS.getCode(), "ok", chatMemberInfoResponse);
    }
}
