/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.exposeimpl;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Throwables;
import com.howbuy.common.exception.BusinessException;
import com.howbuy.crm.wechat.client.base.Response;
import com.howbuy.crm.wechat.client.enums.CompanyNoEnum;
import com.howbuy.crm.wechat.client.enums.ResponseCodeEnum;
import com.howbuy.crm.wechat.client.producer.cmwechatgroup.CmWechatGroupQueryService;
import com.howbuy.crm.wechat.client.producer.req.QueryUserGroupRequest;
import com.howbuy.crm.wechat.client.producer.vo.*;
import com.howbuy.crm.wechat.service.commom.constant.Constants;
import com.howbuy.crm.wechat.service.commom.utils.CompanyNoUtils;
import com.howbuy.crm.wechat.service.service.wechatgroup.WechatGroupUserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Objects;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/5/29 1:48 PM
 * @since JDK 1.8
 */
@DubboService
@Slf4j
public class CmWechatGroupQueryImpl implements CmWechatGroupQueryService {

    @Autowired
    private WechatGroupUserService wechatGroupUserService;


    @Override
    public Response<UserGroupVO> queryUserGroupList(QueryUserGroupRequest queryUserGroupRequest) {
        log.info("queryUserGroupList request:{}", JSON.toJSONString(queryUserGroupRequest));
        List<Integer> deptIdList = queryUserGroupRequest.getDeptIdList();
        if (CollectionUtils.isEmpty(deptIdList)) {
            return new Response<>(ResponseCodeEnum.PARAM_ERROR.getCode(), ResponseCodeEnum.PARAM_ERROR.getDescription(), null);
        }
        String hbOneNo = queryUserGroupRequest.getHbOneNo();
        String wechatNickName = queryUserGroupRequest.getWechatNickName();
        Integer pageNo = queryUserGroupRequest.getPageNo();
        Integer pageSize = queryUserGroupRequest.getPageSize();

        // 使用CompanyNoUtils处理企业编码
        String companyNo = CompanyNoUtils.getCompanyNo(queryUserGroupRequest.getCompanyNo());
        CompanyNoEnum companyNoEnum = CompanyNoUtils.toCompanyNoEnum(companyNo);
        UserGroupVO result = wechatGroupUserService.queryUserGroupList(companyNoEnum,
                hbOneNo, wechatNickName, deptIdList, pageNo, pageSize);
        return Response.ok(result);
    }

    @Override
    public Response<GroupIdVO> getGroupIdByHbOneNo(String hbOneNo) {
        // 使用CompanyNoUtils处理企业编码
        String companyNo = CompanyNoUtils.getDefaultCompanyNo();
        CompanyNoEnum companyNoEnum = CompanyNoUtils.toCompanyNoEnum(companyNo);
        GroupIdVO vo = wechatGroupUserService.queryNormalByHboneNo(companyNoEnum,hbOneNo);
        return Response.ok(vo);
    }

    @Override
    public Response<JoinGroupResultVO> getJoinGroupResult(String hbOneNo, String groupId) {
        if (StringUtils.isEmpty(hbOneNo) || StringUtils.isEmpty(groupId)) {
            return new Response<>(ResponseCodeEnum.PARAM_ERROR.getCode(), ResponseCodeEnum.PARAM_ERROR.getDescription(), null);
        }
        // 使用CompanyNoUtils处理企业编码
        String companyNo = CompanyNoUtils.getDefaultCompanyNo();
        CompanyNoEnum companyNoEnum = CompanyNoUtils.toCompanyNoEnum(companyNo);
        JoinGroupResultVO vo = wechatGroupUserService.queryJoinGroup(companyNoEnum,hbOneNo, groupId);
        return Response.ok(vo);
    }

    @Override
    public Response<GroupBreakStatusVO> getGroupBreakStatus(String groupId) {
        if (StringUtils.isEmpty(groupId)) {
            return new Response<>(ResponseCodeEnum.PARAM_ERROR.getCode(), ResponseCodeEnum.PARAM_ERROR.getDescription(), null);
        }
        GroupBreakStatusVO vo = wechatGroupUserService.queryGroupStatus(groupId);
        if(Objects.isNull(vo)){
            return new Response<>(ResponseCodeEnum.NULL_ERROR.getCode(), ResponseCodeEnum.NULL_ERROR.getDescription(), null);
        }
        return Response.ok(vo);
    }

    @Override
    public Response<List<UserGroupInfoVO>> queryUserGroupInfoByHbOneNo(String hbOneNo) {
        // 使用CompanyNoUtils处理企业编码
        String companyNo = CompanyNoUtils.getDefaultCompanyNo();
        CompanyNoEnum companyNoEnum = CompanyNoUtils.toCompanyNoEnum(companyNo);
        try {
            return Response.ok(wechatGroupUserService.queryUserGroupInfoByHbOneNo(companyNoEnum,hbOneNo));
        } catch (BusinessException be) {
            return new Response<>(ResponseCodeEnum.PARAM_ERROR.getCode(), be.getErrorDesc(), null);
        } catch (Exception e) {
            log.error("queryUserGroupInfoByHbOneNo error:{}", Throwables.getStackTraceAsString(e));
            return new Response<>(ResponseCodeEnum.SYS_ERROR.getCode(), "System error", null);
        }
    }
}