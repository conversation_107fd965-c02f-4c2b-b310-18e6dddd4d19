/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.service.config;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.howbuy.crm.wechat.dao.po.CmWechatApplicationPO;
import com.howbuy.crm.wechat.dao.po.CmWechatCompanyPO;
import com.howbuy.crm.wechat.service.cacheservice.AbstractCacheService;
import com.howbuy.crm.wechat.service.commom.utils.ApplicationUtilityDTO;
import com.howbuy.crm.wechat.service.commom.utils.CorpUtilityDTO;
import com.howbuy.crm.wechat.service.repository.CmWechatCorpConfigRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * @description: (企微基础配置  缓存 service)
 * <AUTHOR>
 * @date 2025/7/24 10:01
 * @since JDK 1.8
 */
@Slf4j
@Service
public class CacheBaseConfigServce  extends AbstractCacheService implements InitializingBean {

    @Autowired
    private CmWechatCorpConfigRepository corpConfigRepository;



    /**
     * 企微 企业 基础配置缓存 key
     */
    private static final String  WECHAT_CORP_CACHE_KEY ="WECHAT_CORP_CONFIG";

    /**
     * 企微 企业 基础配置缓存 key
     */
    private static final String  WECHAT_APPLICATION_CACHE_KEY ="WECHAT_APPLICATION_CONFIG";

    @Override
    public void afterPropertiesSet() throws Exception {
       reloadCorpConfig();
       reloadAppConfig();
    }

    /**
     * 重新加载企微 企业 缓存
     */
    public void reloadCorpConfig() {
        List<CmWechatCompanyPO> corpConfigList = corpConfigRepository. selectCorpConfigList();
        corpConfigList.forEach(companyPO->{
            String companyNo = companyPO.getCompanyNo();
            CorpUtilityDTO corpUtilityDTO = buidlCorpUtilityDTO(companyPO);
            CACHE_SERVICE.putObjToMap(WECHAT_CORP_CACHE_KEY,companyNo,corpUtilityDTO);
            log.info("企微企业companyNo:{} 配置:{}，缓存刷新！", companyNo, JSON.toJSONString(corpUtilityDTO));
        });
    }

   /**
     * 根据companyNo 刷新企微企业缓存
     * @param companyNo
     */
    public void reloadCorpConfigByConpanyNo(String companyNo){
        CmWechatCompanyPO companyPO = corpConfigRepository.getCorpInfoByCompanyNo(companyNo);
        CorpUtilityDTO corpUtilityDTO = buidlCorpUtilityDTO(companyPO);
        CACHE_SERVICE.putObjToMap(WECHAT_CORP_CACHE_KEY,companyNo,corpUtilityDTO);
        log.info("企微企业companyNo:{} 配置:{}，缓存刷新！", companyNo, JSON.toJSONString(corpUtilityDTO));
    }


    /**
     * 构建企微企业配置信息
     * @param companyPO
     * @return
     */
    private CorpUtilityDTO buidlCorpUtilityDTO(CmWechatCompanyPO companyPO){
        String companyNo = companyPO.getCompanyNo();
        CorpUtilityDTO corpUtilityDTO = new CorpUtilityDTO();
        corpUtilityDTO.setCompanyNo(companyNo);
        corpUtilityDTO.setCorpId(companyPO.getCorpId());
        corpUtilityDTO.setCorpType(companyPO.getCorpType());
        corpUtilityDTO.setToken(companyPO.getToken());
        corpUtilityDTO.setEncodingAesKey(companyPO.getEncodingAesKey());
        return corpUtilityDTO;
    }



    /**
     * 根据companyNo 查询有效的公司配置信息
     * @param companyNo  企微-企业主体
     * @return
     */
    public CorpUtilityDTO getCorpConfig(String companyNo) {
        return (CorpUtilityDTO)CACHE_SERVICE.getObjFromMap(WECHAT_CORP_CACHE_KEY, companyNo);
    }

    /**
     * 获取所有公司配置信息
     * @return
     */
    public Map<String,CorpUtilityDTO> getCorpConfigMap() {
        Map<String, CorpUtilityDTO> returnMap= Maps.newHashMap();
        Map<String, Object> cacheMap=CACHE_SERVICE.getFromObjMap(WECHAT_CORP_CACHE_KEY);
        cacheMap.forEach((companyNo,value)->{
            returnMap.put(companyNo,(CorpUtilityDTO)value);
        });
        return returnMap;
    }




    //---------------------------------------------------------------------------------------------------------------------
    /**
     * 重新加载企微 应用 缓存
     */
    public void reloadAppConfig() {
        List<CmWechatApplicationPO> appConfigList = corpConfigRepository.selectApplicationConfigList();
        putConfigListToCache(appConfigList);
    }

    /**
     * 根据companyNo 刷新企微企业应用 缓存
     * @param companyNo
     */
    public void reloadAppConfigByCompanyNo(String companyNo) {
        List<CmWechatApplicationPO> appConfigList = corpConfigRepository.selectApplicationConfigList();
        appConfigList.removeIf(applicationInfo -> !applicationInfo.getCompanyNo().equals(companyNo));
        putConfigListToCache(appConfigList);
    }


    /**
     * 批量添加企微企业应用配置信息 --> cache中
     * @param appConfigList
     */
    private void putConfigListToCache(List<CmWechatApplicationPO> appConfigList){
        for (CmWechatApplicationPO applicationInfo : appConfigList) {
            String applicationCode = applicationInfo.getApplicationCode();
            //归属 公司
            String companyNo = applicationInfo.getCompanyNo();
            CmWechatCompanyPO corpInfo = corpConfigRepository.getCorpInfoByCompanyNo(companyNo);
            if (corpInfo == null) {
                log.error("企微应用：{} 对应companyNo:{},配置不存在！", applicationCode, companyNo);
                continue;
            }


            ApplicationUtilityDTO utilityDTO = new ApplicationUtilityDTO();
            //应用信息
            utilityDTO.setApplicationCode(applicationCode);
            utilityDTO.setApplicationType(applicationInfo.getApplicationType());
            utilityDTO.setAgentId(applicationInfo.getAgentId());
            utilityDTO.setAccessSecret(applicationInfo.getAccessSecret());

            //补充该公司信息
            utilityDTO.setCompanyNo(companyNo);
            utilityDTO.setCorpId(corpInfo.getCorpId());
            utilityDTO.setToken(corpInfo.getToken());
            utilityDTO.setEncodingAesKey(corpInfo.getEncodingAesKey());
            CACHE_SERVICE.putObjToMap(WECHAT_APPLICATION_CACHE_KEY, applicationCode, utilityDTO);
            log.info("企微应用：{}，所属企业companyNo:{} 配置:{}，缓存刷新！", applicationCode, companyNo, JSON.toJSONString(utilityDTO));
        }
    }

    /**
     * 根据 applicationCode 查询有效的公司配置信息
     * @param applicationCode  企微-应用主体
     * @return
     */
    public ApplicationUtilityDTO getApplicationConfig(String applicationCode) {
        return (ApplicationUtilityDTO)CACHE_SERVICE.getObjFromMap(WECHAT_APPLICATION_CACHE_KEY, applicationCode);
    }

    /**
     * 获取所有公司应用配置信息
     * @return
     */
    public Map<String,ApplicationUtilityDTO> getApplicationConfigMap() {
        Map<String, ApplicationUtilityDTO> returnMap= Maps.newHashMap();
        Map<String, Object> cacheMap=CACHE_SERVICE.getFromObjMap(WECHAT_APPLICATION_CACHE_KEY);
        cacheMap.forEach((applicationCode,utilityDTO)->{
            returnMap.put(applicationCode,(ApplicationUtilityDTO)utilityDTO);
        });
        return returnMap;
    }


}