/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.service.config;

import com.alibaba.fastjson.JSON;
import com.howbuy.crm.wechat.client.enums.CompanyNoEnum;
import com.howbuy.crm.wechat.client.enums.ResponseCodeEnum;
import com.howbuy.crm.wechat.client.enums.WechatApplicationEnum;
import com.howbuy.crm.wechat.service.commom.aes.WXBizMsgCrypt;
import com.howbuy.crm.wechat.service.commom.exception.BusinessException;
import com.howbuy.crm.wechat.service.commom.utils.ApplicationUtilityDTO;
import com.howbuy.crm.wechat.service.commom.utils.CompanyNoUtils;
import com.howbuy.crm.wechat.service.commom.utils.CorpUtilityDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @description: (企微基础配置service)
 * <AUTHOR>
 * @date 2025/7/24 10:01
 * @since JDK 1.8
 */
@Slf4j
@Service
public class BaseConfigServce {
    @Autowired
    private  CacheBaseConfigServce cacheBaseConfigServce;

    /**
     * 企微 应用 类型
     *企业微信-内建应用- 客户联系
     */
    private static final String  APPLICATION_TYPE_CUSTOMER= "customer";


    /**
     * 获取企业微信corpId
     * @param companyNoEnum  企微-企业主体
     * @return corpId
     * @deprecated 请使用 {@link #getCorpId(String)} 方法
     */
    @Deprecated
    public String getCorpId(CompanyNoEnum companyNoEnum) {
        String companyNo = companyNoEnum != null ? companyNoEnum.getCode() : DEFAULT_COMPANY_NO;
        return getCorpId(companyNo);
    }



    /**
     * 根据corpId获取企业编码
     * @param corpId 企业ID
     * @return 企业编码
     */
    public String getCompanyNoByCorpId(String corpId) {
        if (StringUtils.isBlank(corpId)) {
            log.warn("企业ID为空，返回默认企业编码: {}", DEFAULT_COMPANY_NO);
            return DEFAULT_COMPANY_NO;
        }

        Map<String, CorpUtilityDTO> corpConfigMap = cacheBaseConfigServce.getCorpConfigMap();
        for (Map.Entry<String, CorpUtilityDTO> entry : corpConfigMap.entrySet()) {
            String companyNo = entry.getKey();
            CorpUtilityDTO utilityDTO = entry.getValue();
            if (corpId.equals(utilityDTO.getCorpId())) {
                return companyNo;
            }
        }

        log.warn("未找到对应的企业编码，corpId: {}，返回默认值: {}", corpId, DEFAULT_COMPANY_NO);
        return DEFAULT_COMPANY_NO;
    }

    /**
     * 获取企业微信corpId
     * @param corpId 企业ID
     * @return 企业编码枚举
     * @deprecated 请使用 {@link #getCompanyNoByCorpId(String)} 方法
     */
    @Deprecated
    public CompanyNoEnum getCompanyNoEnum(String corpId) {
        String companyNo = getCompanyNoByCorpId(corpId);
        return CompanyNoUtils.toCompanyNoEnum(companyNo);
    }

    /**
     * @description: 获取企业微信工具类对象
     * @param companyNoEnum 企业编码枚举
     * @return com.howbuy.crm.wechat.service.commom.utils.CorpUtilityDTO
     * @author: haoran.zhang
     * @date: 2025/7/11 15:01
     * @since JDK 1.8
     * @deprecated 请使用 {@link #getCorpUtilityDTO(String)} 方法
     */
    @Deprecated
    public CorpUtilityDTO getCorpUtilityDTO(CompanyNoEnum companyNoEnum) {
        String companyNo = companyNoEnum != null ? companyNoEnum.getCode() : DEFAULT_COMPANY_NO;
        return getCorpUtilityDTO(companyNo);
    }


    /**
     * @description:构建WXBizMsgCrypt对象
     * @param companyNoEnum 企业编码枚举
     * @return com.howbuy.crm.wechat.service.commom.utils.WXBizMsgCrypt
     * @author: yu.zhang
     * @date: 2023/6/8 15:09
     * @since JDK 1.8
     * @deprecated 请使用 {@link #buildWXBizMsgCrypt(String)} 方法
     */
    @Deprecated
    public WXBizMsgCrypt buildWXBizMsgCrypt(CompanyNoEnum companyNoEnum) {
        String companyNo = companyNoEnum != null ? companyNoEnum.getCode() : DEFAULT_COMPANY_NO;
        return buildWXBizMsgCrypt(companyNo);
    }



    /**
     * @description: 获取应用工具类对象
     * @param applicationEnum 应用枚举
     * @return com.howbuy.crm.wechat.service.commom.utils.ApplicationUtilityDTO
     * @author: haoran.zhang
     * @date: 2025/7/11 15:01
     * @since JDK 1.8
     * @deprecated 请使用 {@link #getApplicationUtilityDTO(String)} 方法
     */
    @Deprecated
    public ApplicationUtilityDTO getApplicationUtilityDTO(WechatApplicationEnum applicationEnum) {
        String applicationCode = applicationEnum != null ? applicationEnum.getCode() : null;
        if (applicationCode == null) {
            throw new BusinessException(ResponseCodeEnum.SYS_CONFIG_ERROR);
        }
        return getApplicationUtilityDTO(applicationCode);
    }


    /**
     * @description: 根据公司编号获取 [企业微信-客户联系]  应用枚举
     * @param companyNoEnum 企业编码枚举
     * @return com.howbuy.crm.wechat.service.commom.enums.WechatApplicationEnum
     * @author: haoran.zhang
     * @date: 2025/7/11 15:01
     * @since JDK 1.8
     * @deprecated 请使用 {@link #getCustApplicationUtilityDTO(String)} 方法
     */
    @Deprecated
    public WechatApplicationEnum getCustApplicationEnum(CompanyNoEnum companyNoEnum) {
        String companyNo = companyNoEnum != null ? companyNoEnum.getCode() : DEFAULT_COMPANY_NO;
        ApplicationUtilityDTO appConfig = getCustApplicationUtilityDTO(companyNo);
        if (appConfig == null) {
            return null;
        }
        // 兼容性转换
        return WechatApplicationEnum.getEnum(appConfig.getApplicationCode());
    }

    // ==================== 新增基于companyNo字符串的方法 ====================

    /**
     * 默认企业编码（好买财富）
     */
    private static final String DEFAULT_COMPANY_NO = "1";

    /**
     * 获取企业微信corpId
     * @param companyNo 企业编码
     * @return corpId
     */
    public String getCorpId(String companyNo) {
        CorpUtilityDTO utilityDTO = getCorpUtilityDTO(companyNo);
        if (utilityDTO == null) {
            throw new BusinessException(ResponseCodeEnum.SYS_CONFIG_ERROR);
        }
        return utilityDTO.getCorpId();
    }

    /**
     * 获取企业微信工具类对象
     * @param companyNo 企业编码
     * @return 企业配置信息
     */
    public CorpUtilityDTO getCorpUtilityDTO(String companyNo) {
        if (StringUtils.isBlank(companyNo)) {
            companyNo = DEFAULT_COMPANY_NO; // 默认好买财富
        }
        CorpUtilityDTO utilityDTO = cacheBaseConfigServce.getCorpConfig(companyNo);
        if (utilityDTO == null) {
            log.error("企业配置不存在，companyNo: {}", companyNo);
            throw new BusinessException(ResponseCodeEnum.SYS_CONFIG_ERROR);
        }
        return utilityDTO;
    }

    /**
     * 根据企业编码获取客户联系应用配置
     * @param companyNo 企业编码
     * @return 客户联系应用配置
     */
    public ApplicationUtilityDTO getCustApplicationUtilityDTO(String companyNo) {
        Map<String, ApplicationUtilityDTO> cacheMap = cacheBaseConfigServce.getApplicationConfigMap();
        List<ApplicationUtilityDTO> custAppList = cacheMap.values().stream()
                .filter(app -> companyNo.equals(app.getCompanyNo()))
                .filter(app -> APPLICATION_TYPE_CUSTOMER.equals(app.getApplicationType()))
                .collect(Collectors.toList());

        if (custAppList.isEmpty()) {
            log.error("企业客户联系应用配置不存在，companyNo: {}", companyNo);
            return null;
        }
        return custAppList.get(0);
    }

    /**
     * 构建WXBizMsgCrypt对象
     * @param companyNo 企业编码
     * @return WXBizMsgCrypt对象
     */
    public WXBizMsgCrypt buildWXBizMsgCrypt(String companyNo) {
        WXBizMsgCrypt wxcpt = null;
        try {
            CorpUtilityDTO corpUtilityDTO = getCorpUtilityDTO(companyNo);
            wxcpt = new WXBizMsgCrypt(corpUtilityDTO.getToken(), corpUtilityDTO.getEncodingAesKey(), corpUtilityDTO.getCorpId());
        } catch (Exception e) {
            log.error("buildWXBizMsgCrypt Exception:{}", e.getMessage(), e);
        }
        return wxcpt;
    }

    /**
     * 根据应用编码获取应用工具类对象
     * @param applicationCode 应用编码
     * @return 应用配置信息
     */
    public ApplicationUtilityDTO getApplicationUtilityDTO(String applicationCode) {
        ApplicationUtilityDTO utilityDTO = cacheBaseConfigServce.getApplicationConfig(applicationCode);
        if (utilityDTO == null) {
            log.info("企微应用：{} 配置不存在！", applicationCode);
            throw new BusinessException(ResponseCodeEnum.SYS_CONFIG_ERROR);
        }
        return utilityDTO;
    }

}