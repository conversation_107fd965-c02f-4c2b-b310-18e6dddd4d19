/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.service.config;

import com.howbuy.crm.wechat.service.commom.utils.CorpUtilityDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @description: 企业配置动态管理器
 * <AUTHOR>
 * @date 2025-08-13 16:45:36
 * @since JDK 1.8
 */
@Service
@Slf4j
public class CompanyConfigManager {
    
    @Autowired
    private CacheBaseConfigServce cacheBaseConfigServce;
    
    /**
     * 默认企业编码（向后兼容）
     */
    private static final String DEFAULT_COMPANY_NO = "1"; // HOWBUY_WEALTH
    
    /**
     * 获取所有有效企业配置
     * @return 企业配置列表
     */
    public List<CorpUtilityDTO> getAllCompanyConfigs() {
        Map<String, CorpUtilityDTO> configMap = cacheBaseConfigServce.getCorpConfigMap();
        return new ArrayList<>(configMap.values());
    }
    
    /**
     * 根据企业编码获取配置
     * @param companyNo 企业编码
     * @return 企业配置信息
     */
    public CorpUtilityDTO getCompanyConfig(String companyNo) {
        if (StringUtils.isBlank(companyNo)) {
            log.warn("企业编码为空，使用默认值: {}", DEFAULT_COMPANY_NO);
            companyNo = DEFAULT_COMPANY_NO;
        }
        return cacheBaseConfigServce.getCorpConfig(companyNo);
    }
    
    /**
     * 验证企业编码是否有效
     * @param companyNo 企业编码
     * @return 是否有效
     */
    public boolean isValidCompanyNo(String companyNo) {
        return getCompanyConfig(companyNo) != null;
    }
    
    /**
     * 获取默认企业编码
     * @return 默认企业编码
     */
    public String getDefaultCompanyNo() {
        return DEFAULT_COMPANY_NO;
    }
    
    /**
     * 根据corpId获取企业编码
     * @param corpId 企业ID
     * @return 企业编码
     */
    public String getCompanyNoByCorpId(String corpId) {
        if (StringUtils.isBlank(corpId)) {
            log.warn("企业ID为空，返回默认企业编码: {}", DEFAULT_COMPANY_NO);
            return DEFAULT_COMPANY_NO;
        }
        
        Map<String, CorpUtilityDTO> configMap = cacheBaseConfigServce.getCorpConfigMap();
        for (Map.Entry<String, CorpUtilityDTO> entry : configMap.entrySet()) {
            String companyNo = entry.getKey();
            CorpUtilityDTO utilityDTO = entry.getValue();
            if (corpId.equals(utilityDTO.getCorpId())) {
                return companyNo;
            }
        }
        
        log.warn("未找到对应的企业编码，corpId: {}，返回默认值: {}", corpId, DEFAULT_COMPANY_NO);
        return DEFAULT_COMPANY_NO;
    }
}
