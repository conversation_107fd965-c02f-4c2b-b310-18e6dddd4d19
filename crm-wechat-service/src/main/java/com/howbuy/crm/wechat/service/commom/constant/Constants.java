/**
 *Copyright (c) 2016, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.crm.wechat.service.commom.constant;

import java.util.Arrays;
import java.util.List;

/**
 * @description:常量类
 * @reason:
 * <AUTHOR>
 * @date 2023/3/8 17:18
 * @since JDK 1.8
 */
public class Constants {

    public static final String METHOD_GET = "Get";

    public static final String METHOD_POST = "Post";
    /**
     * 请求id
     */
    public static final String REQ_ID = "reqId";
    /**
     * 缓存锁key前缀
     * 微信TOKEN缓存key前缀
     */
    public static final String ACCESS_TOKEN_PARTITION = "WECHAT_KEY_PREFIX|";

    /**
     * 微信JS_SDK缓存key前缀
     */
    public static final String WECHAT_JS_SDK_CACHE_KEY = "WECHAT_KEY_PREFIX|JS_SDK_";

    public static final String CONTENT_TYPE = "Content-Type";

    public static final String DEFAULT_CHARSET = "utf-8";

    public static final String WECHAT_ERRMSG = "ok";

    public static final String WECHAT_ERR_CODE_KEY = "errcode";

    public static final String WECHAT_ERR_MSG_KEY = "errmsg";

    public static final List<String> WECHAT_TOKEN_EXPIRED_CODE = Arrays.asList("42001", "40014");
    public static final String DISMISS_CODE = "49008";
    public static final String SUCCESS_CODE = "0";

    /** 资产中心配置 */
    public static final String ZK_ACC_CENTER = "acc-center-server";

    /** crm配置 */
    public static final String ZK_CRM_CORE = "crm-core-server";
    public static final String ZK_CRM_NT = "crm-nt-server";
    public static final String ZK_CRM_WECHAT_REMOTE = "crm-wechat-remote";

    /** 消息中心配置 */
    public static final String ZK_MESSAGE_PUBLIC = "message-remote";


    /**
     * 微信服务端交互 域名
     */
    public static final String INTERACT_DOMAIN_URL = "https://qyapi.weixin.qq.com/cgi-bin/";


    /**
     * 企业微信: 取TOKEN的PATH
     */
    public static final String TOKEN_PATH = "gettoken?corpid=%s&corpsecret=%s";
    /**
     * 企业微信: 发消息PATH
     */
    public static final String MSG_PATH = "message/send?access_token=%s";

    /**
     * 每批commit的个数
     */
    public static final int BATCH_COUNT = 200;


    /**
     * 分页读取文件 每次的条数 count
     */
    public static final int FILE_LINE_COUNT = 5000;

    /**
     * 查询数据库 最大条数
     */
    public static final int MAX_QUERY_DB_COUNT = 49999;


    /**
     * 每批commit的个数
     */
    public static final int THREAD_MAX_POOL_SIZE = 8;

    /**
     * 系统异常，businessException 使用。
     */
    public static final String SYSTEM_ERROR = "9999";


    /**
     * 企业微信: 获取访问用户身份的PATH
     */
    public static final String WECHAT_USER_ID_PATH = "auth/getuserinfo";

    /**
     * 企业微信: 取企业JS_API_TICKET的PATH
     *
     */
    public static final String WECHAT_ENTERPRISE_JS_API_TICKET_PATH = "get_jsapi_ticket";

    /**
     * 企业微信: 取应用JS_API_TICKET的PATH
     */
    public static final String WECHAT_APP_JS_API_TICKET_PATH = "ticket/get";

    /**
     * 企业微信: 上传临时素材
     */
    public static final String WECHAT_MEDIA_UPLOAD = "media/upload?access_token=%s&type=%s";
}
