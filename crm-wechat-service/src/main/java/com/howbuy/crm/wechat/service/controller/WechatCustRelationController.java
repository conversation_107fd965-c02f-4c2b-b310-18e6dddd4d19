/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.controller;

import com.howbuy.crm.wechat.client.enums.CompanyNoEnum;
import com.howbuy.crm.wechat.dao.po.custrelation.CustConsultRelationPO;
import com.howbuy.crm.wechat.dao.vo.custrelation.CustConsultRelationVO;
import com.howbuy.crm.wechat.service.commom.utils.CompanyNoUtils;
import com.howbuy.crm.wechat.service.service.custrelation.WechatCustRelationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @description: (企业微信  客户关系 controller )
 * <AUTHOR>
 * @date 2023/11/24 19:23
 * @since JDK 1.8
 */
@Slf4j
@RestController
@RequestMapping("/wechatcustrelation")
public class WechatCustRelationController {

    @Autowired
    private WechatCustRelationService wechatCustRelationService;


    /**
     * @api {POST} /wechatcustrelation/selectrelationlistbyvo selectRelationListByVo()
     * @apiVersion 1.0.0
     * @apiGroup WechatCustRelationController
     * @apiName selectRelationListByVo()
     * @apiDescription 批量查询客户hboneNo 和 投顾consCode 关系
     * @apiParam (请求体) {Array} requestBody
     * @apiParam (请求体) {String} requestBody.hboneNo 客户信息-一账通号
     * @apiParam (请求体) {String} requestBody.conscode 投顾号
     * @apiParam (请求参数) {String} companyNo 企业编码（可选，默认为1-好买财富）
     * @apiParamExample 请求体示例
     * [{"conscode":"t","hboneNo":"AVD8"}]
     * @apiSuccess (响应结果) {Array} response
     * @apiSuccess (响应结果) {Number} response.id id
     * @apiSuccess (响应结果) {String} response.externalUserId 客户信息-外部应用用户ID
     * @apiSuccess (响应结果) {String} response.hboneNo 客户信息-一账通号
     * @apiSuccess (响应结果) {String} response.unionid 客户信息-微信UnionId
     * @apiSuccess (响应结果) {String} response.conscode 投顾号
     * @apiSuccess (响应结果) {String} response.status 状态1新增  2删除客户   3被客户删除
     * @apiSuccess (响应结果) {Number} response.addTime 添加时间
     * @apiSuccess (响应结果) {Number} response.delTime 删除时间
     * @apiSuccess (响应结果) {String} response.companyNo 企业编码1好买财富2好买基金
     * @apiSuccess (响应结果) {String} response.addWay 添加客户的来源
     * @apiSuccess (响应结果) {String} response.state 添加客户的渠道
     * @apiSuccessExample 响应结果示例
     * [{"unionid":"UOHjML","addTime":2995865321154,"companyNo":"SBWNV","externalUserId":"ucxnUUut6","addWay":"j","delTime":1351503081579,"id":1390,"state":"1xRF2","conscode":"o1","hboneNo":"oVgbtOYf","status":"GZVHN"}]
     */
    @PostMapping("/selectrelationlistbyvo")
    @ResponseBody
    public List<CustConsultRelationPO> selectRelationListByVo(
            @RequestBody List<CustConsultRelationVO> voList,
            @RequestParam(required = false) String companyNo) {

        // 使用新的工具类处理企业编码
        String processedCompanyNo = CompanyNoUtils.getCompanyNo(companyNo);
        CompanyNoEnum companyNoEnum = CompanyNoUtils.toCompanyNoEnum(processedCompanyNo);

        return wechatCustRelationService.selectRelationListByVo(companyNoEnum, voList);
    }

}