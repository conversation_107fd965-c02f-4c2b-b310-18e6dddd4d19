/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.service.wechatmaterial;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.howbuy.crm.wechat.client.base.Response;
import com.howbuy.crm.wechat.client.domain.request.wechatmaterial.WechatUploadMediaRequest;
import com.howbuy.crm.wechat.client.domain.response.wechatmaterial.WechatUploadMediaVO;
import com.howbuy.crm.wechat.client.enums.ResponseCodeEnum;
import com.howbuy.crm.wechat.client.enums.WechatApplicationEnum;
import com.howbuy.crm.wechat.service.commom.constant.Constants;
import com.howbuy.crm.wechat.service.outerservice.wechatapi.WeChatCommonOuterService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description: 微信素材管理服务
 * @date 2024/9/19 10:11
 * @since JDK 1.8
 */
@Slf4j
@Service
public class WechatMaterialService {

    @Resource
    private WeChatCommonOuterService weChatCommonOuterService;

    /**
     * @description:上传素材
     * @param request
     * @return com.howbuy.crm.wechat.client.base.Response<com.howbuy.crm.wechat.client.domain.response.wechatmaterial.WechatUploadMediaVO>
     * <AUTHOR>
     * @date 2024/9/19 17:16
     * @since JDK 1.8
     */
    public Response<WechatUploadMediaVO> uploadMediaFile(WechatUploadMediaRequest request) {
        WechatApplicationEnum applicationEnum = WechatApplicationEnum.getEnum(request.getWechatAppEnumKey());
        Assert.notNull(applicationEnum, "企微应用秘钥枚举转换为空");

        String resultJson = weChatCommonOuterService.uploadMaterial(applicationEnum, request.getBytes(), request.getFileName(), request.getType());
        JSONObject mapData = JSON.parseObject(resultJson);
        //结果判断
        String errCode = mapData.getString(Constants.WECHAT_ERR_CODE_KEY);
        String errMsg = mapData.getString(Constants.WECHAT_ERR_MSG_KEY);
        if (!Constants.SUCCESS_CODE.equals(errCode)) {
            log.info("uploadMediaFile >>> 上传素材错误，企微应用:{},errCode:{},errMsg:{}", request.getWechatAppEnumKey(), errCode, errMsg);
            return new Response<>(ResponseCodeEnum.UNKNOWN_ERROR.getCode(), errMsg, null);
        }

        WechatUploadMediaVO mediaVO = new WechatUploadMediaVO();
        mediaVO.setMediaId(mapData.getString("media_id"));
        mediaVO.setType(mapData.getString("type"));
        mediaVO.setCreatedAt(mapData.getString("created_at"));
        return Response.ok(mediaVO);
    }

}
