/**
 * Copyright (c) 2025, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.commom.utils;

import java.io.Serializable;

/**
 * @description: (企业微信-企业主体-组件DTO)
 * <AUTHOR>
 * @date 2025/7/11 13:44
 * @since JDK 1.8
 */
public class CorpUtilityDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 企微-企业主体
     */
    private String companyNo;

    /**
     * 每个企业都拥有唯一的corpid，获取此信息可在管理后台“我的企业”－“企业信息”下查看“企业ID”
     */
    private String corpId;

    /**
     * 企业类型。1-企业内部 2-第三方应用 3-服务商代开发 4-智慧硬件开发
     */
    private String corpType;


    /**
     * 企业微信后台，开发者设置的token
     */
    private String token;

    /**
     * 企业微信后台，开发者设置的EncodingAESKey
     */
    private String encodingAesKey;


    public CorpUtilityDTO() {
    }

    public String getCompanyNo() {
        return companyNo;
    }

    public void setCompanyNo(String companyNo) {
        this.companyNo = companyNo;
    }

    public String getCorpId() {
        return corpId;
    }

    public void setCorpId(String corpId) {
        this.corpId = corpId;
    }

    public String getCorpType() {
        return corpType;
    }

    public void setCorpType(String corpType) {
        this.corpType = corpType;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getEncodingAesKey() {
        return encodingAesKey;
    }

    public void setEncodingAesKey(String encodingAesKey) {
        this.encodingAesKey = encodingAesKey;
    }
}