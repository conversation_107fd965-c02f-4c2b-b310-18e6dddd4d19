/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.service.config;

import com.howbuy.crm.wechat.service.commom.utils.ApplicationUtilityDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @description: 应用配置动态管理器
 * <AUTHOR>
 * @date 2025-08-13 16:45:36
 * @since JDK 1.8
 */
@Service
@Slf4j
public class ApplicationConfigManager {
    
    @Autowired
    private CacheBaseConfigServce cacheBaseConfigServce;
    
    /**
     * 应用类型：客户联系
     */
    private static final String APPLICATION_TYPE_CUSTOMER = "customer";
    
    /**
     * 应用类型：CRM
     */
    private static final String APPLICATION_TYPE_CRM = "crm";
    
    /**
     * 应用类型：消息发送
     */
    private static final String APPLICATION_TYPE_MESSAGE = "message";
    
    /**
     * 根据企业编码获取客户联系应用配置
     * @param companyNo 企业编码
     * @return 客户联系应用配置
     */
    public ApplicationUtilityDTO getCustomerApplication(String companyNo) {
        if (StringUtils.isBlank(companyNo)) {
            log.warn("企业编码为空，无法获取客户联系应用配置");
            return null;
        }
        
        Map<String, ApplicationUtilityDTO> configMap = cacheBaseConfigServce.getApplicationConfigMap();
        return configMap.values().stream()
                .filter(app -> companyNo.equals(app.getCompanyNo()))
                .filter(app -> APPLICATION_TYPE_CUSTOMER.equals(app.getApplicationType()))
                .findFirst()
                .orElse(null);
    }
    
    /**
     * 根据企业编码获取所有应用配置
     * @param companyNo 企业编码
     * @return 应用配置列表
     */
    public List<ApplicationUtilityDTO> getApplicationsByCompany(String companyNo) {
        if (StringUtils.isBlank(companyNo)) {
            log.warn("企业编码为空，返回空列表");
            return List.of();
        }
        
        Map<String, ApplicationUtilityDTO> configMap = cacheBaseConfigServce.getApplicationConfigMap();
        return configMap.values().stream()
                .filter(app -> companyNo.equals(app.getCompanyNo()))
                .collect(Collectors.toList());
    }
    
    /**
     * 根据企业编码获取发送消息的应用配置
     * @param companyNo 企业编码
     * @return 发送消息的应用配置
     */
    public ApplicationUtilityDTO getSendMsgApplication(String companyNo) {
        if (StringUtils.isBlank(companyNo)) {
            log.warn("企业编码为空，无法获取发送消息应用配置");
            return null;
        }
        
        List<ApplicationUtilityDTO> apps = getApplicationsByCompany(companyNo);
        
        // 优先查找CRM类型的应用
        ApplicationUtilityDTO crmApp = apps.stream()
                .filter(app -> APPLICATION_TYPE_CRM.equals(app.getApplicationType()))
                .findFirst()
                .orElse(null);
        
        if (crmApp != null) {
            return crmApp;
        }
        
        // 其次查找消息类型的应用
        ApplicationUtilityDTO msgApp = apps.stream()
                .filter(app -> APPLICATION_TYPE_MESSAGE.equals(app.getApplicationType()))
                .findFirst()
                .orElse(null);
        
        if (msgApp != null) {
            return msgApp;
        }
        
        // 最后返回第一个可用的应用
        return apps.stream().findFirst().orElse(null);
    }
    
    /**
     * 根据应用编码获取应用配置
     * @param applicationCode 应用编码
     * @return 应用配置
     */
    public ApplicationUtilityDTO getApplicationByCode(String applicationCode) {
        if (StringUtils.isBlank(applicationCode)) {
            log.warn("应用编码为空，无法获取应用配置");
            return null;
        }
        
        return cacheBaseConfigServce.getApplicationConfig(applicationCode);
    }
    
    /**
     * 验证应用编码是否有效
     * @param applicationCode 应用编码
     * @return 是否有效
     */
    public boolean isValidApplicationCode(String applicationCode) {
        return getApplicationByCode(applicationCode) != null;
    }
}
