package com.howbuy.crm.wechat.service.business;

import com.google.common.collect.Lists;
import com.howbuy.crm.wechat.client.enums.CompanyNoEnum;
import com.howbuy.crm.wechat.dao.po.CmWechatDeptPO;
import com.howbuy.crm.wechat.dao.po.CmWechatEmpPO;
import com.howbuy.crm.wechat.service.domain.externaluser.ExternalDeptDTOExternal;
import com.howbuy.crm.wechat.service.outerservice.wechatapi.WechatDepartmentOuterService;
import com.howbuy.crm.wechat.service.outerservice.wechatapi.WechatUserOuterService;
import com.howbuy.crm.wechat.service.outerservice.wechatapi.domain.ExternalUserInfoDTO;
import com.howbuy.crm.wechat.service.outerservice.wechatapi.vo.WechatQueryUserVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @description:企业微信数据获取工具类
 * @author: yu.zhang
 * @date: 2023/6/26 18:23
 * @since JDK 1.8
 */
@Slf4j
@Component
public class WechatDataBusiness {


    @Autowired
    private WechatDepartmentOuterService wechatDepartmentOuterService;
    @Autowired
    private WechatUserOuterService wechatUserOuterService;
    /**
     * 工号名称
     */
    private static final String EMP_NAME = "工号";


    /**
     * @description:获取全量部门列表数据
     * @param companyNoEnum
     * @return java.util.List<com.howbuy.crm.wechat.dao.po.CmWechatDeptPO>
     * @author: yu.zhang
     * @date: 2023/6/26 18:23
     * @since JDK 1.8
     */
    public List<CmWechatDeptPO> getFullDeptListByCompanyNo(CompanyNoEnum companyNoEnum){
        List<CmWechatDeptPO> resultList = Lists.newArrayList();
        List<ExternalDeptDTOExternal>  deptDTOList=
                  wechatDepartmentOuterService.queryDeptListByCompanyNo( companyNoEnum);
        deptDTOList.forEach(deptDTO->{
            CmWechatDeptPO cmWechatDeptPO = new CmWechatDeptPO();
            cmWechatDeptPO.setDeptId(deptDTO.getId());
            cmWechatDeptPO.setDeptName(deptDTO.getName());
            cmWechatDeptPO.setParentDeptId(deptDTO.getParentId());
            cmWechatDeptPO.setCompanyNo(companyNoEnum.getCode());
            resultList.add(cmWechatDeptPO);
        });
        return resultList;
    }



    /**
     * @description:获取全量部门成员详情数据
     * @param companyNoEnum
     * @return java.util.List<com.howbuy.crm.wechat.dao.po.CmWechatEmpPO>
     * @author: haoran.zhang
     * @date: 2023/6/26 18:23
     * @since JDK 1.8
     */
    public List<CmWechatEmpPO> getFullDeptUserDetailListByCompanyNo(CompanyNoEnum companyNoEnum) {

        List<CmWechatEmpPO> resultList = Lists.newArrayList();

        WechatQueryUserVO queryUserVO=new WechatQueryUserVO();
        queryUserVO.setCompanyNo(companyNoEnum.getCode());
        //根部门：1
//       好买财富： 根部门= 1-好买
//       好买基金销售： 根部门= 1-好买基金销售
        queryUserVO.setDepartmentId("1");
        //1：递归调用
        queryUserVO.setFetchChild("1");

        List<ExternalUserInfoDTO> userList =wechatUserOuterService.queryUserListByCompanyNo(queryUserVO);
        if(CollectionUtils.isNotEmpty(userList)){
            userList.forEach(user -> {
                CmWechatEmpPO wechatEmp = new CmWechatEmpPO();
                wechatEmp.setEmpId(user.getUserid());
                wechatEmp.setEmpName(user.getName());
                wechatEmp.setDeptId(user.getMainDepartment());
                wechatEmp.setCompanyNo(companyNoEnum.getCode());
                wechatEmp.setThumbAvatar(user.getThumbAvatar());
                wechatEmp.setAvatar(user.getAvatar());
                wechatEmp.setQrCode(user.getQrCode());
                wechatEmp.setEmail(user.getEmail());
                //获取：工号
                wechatEmp.setEmpWorkId(getEmpWorkId(user));

                resultList.add(wechatEmp);
            });
        }

        return resultList;
    }

    /**
     * @description:(获取：工号)
     * @param user
     * @return java.lang.String
     * @author: haoran.zhang
     * @date: 2025/7/21 10:14
     * @since JDK 1.8
     */
    private String getEmpWorkId(ExternalUserInfoDTO user){
        if(user.getExtattr()!=null){
            if(CollectionUtils.isNotEmpty(user.getExtattr().getAttrs())){
                ExternalUserInfoDTO.Attr attr= user.getExtattr().getAttrs().stream().filter(attrInfo -> EMP_NAME.equals(attrInfo.getName())).findFirst().orElse(null);
                if(attr!=null && attr.getText()!=null){
                    return attr.getText().getValue();
                }
            }
        }
        return null;
    }

}
