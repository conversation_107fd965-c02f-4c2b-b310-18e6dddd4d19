package com.howbuy.crm.wechat.dao.mapper;

import com.howbuy.crm.wechat.dao.bo.EmpDeptInfoBo;
import com.howbuy.crm.wechat.dao.po.CmWechatEmpPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.ArrayList;
import java.util.List;

/**
 * @description: crm-wechat 
 * @author: yu.zhang
 * @date: 2023/6/28 10:03 
 * @since JDK 1.8
 * @version: 1.0
 */
@Mapper
public interface CmWechatEmpMapper {
    /**
     * delete by primary key
     * @param id primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Long id);

    /**
     * insert record to table
     * @param record the record
     * @return insert count
     */
    int insert(CmWechatEmpPO record);

    /**
     * insert record to table selective
     * @param record the record
     * @return insert count
     */
    int insertSelective(CmWechatEmpPO record);

    /**
     * select by primary key
     * @param id primary key
     * @return object by primary key
     */
    CmWechatEmpPO selectByPrimaryKey(Long id);

    /**
     * update record selective
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(CmWechatEmpPO record);

    /**
     * update record
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(CmWechatEmpPO record);

    /**
     * @description: 根据员工编号查询员工信息
     * @param companyNo	企业编码
     * @param empId	员工userId
     * @return com.howbuy.crm.wechat.dao.bo.EmpDeptInfoBo 员工信息
     * @author: jin.wang03
     * @date: 2023/10/30 10:55
     * @since JDK 1.8
     */
    EmpDeptInfoBo getDeptInfoByEmpId(@Param("companyNo") String companyNo,@Param("empId") String empId);

    /**
     * @description:(根据部门id获取部门下所有员工)
     * @param companyNo	企业编码
     * @param deptIdList
     * @return java.util.List<java.lang.String>
     * @author: shuai.zhang
     * @date: 2024/6/17 17:16
     * @since JDK 1.8
     */
    List<String> selectEmpIdByDeptList(@Param("companyNo") String companyNo, @Param("deptIdList") List<String> deptIdList);
}