package com.howbuy.crm.wechat.dao.mapper;

import com.howbuy.crm.wechat.dao.po.CmWechatApplicationPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2025/7/24 8:34
 * @since JDK 1.8
 */
@Mapper
public interface CmWechatApplicationMapper {
    int deleteByPrimaryKey(String applicationCode);

    int insert(CmWechatApplicationPO record);

    int insertSelective(CmWechatApplicationPO record);

    CmWechatApplicationPO selectByPrimaryKey(String applicationCode);

    int updateByPrimaryKeySelective(CmWechatApplicationPO record);

    int updateByPrimaryKey(CmWechatApplicationPO record);



    /**
     * 根据应用编码查询配置信息
     * @param applicationCode
     * @return CmWechatApplicationPO
     */
    CmWechatApplicationPO selectConfigByApplicationCode(@Param("applicationCode") String applicationCode);

    /**
     * 查询有效的应用配置信息列表
     * @return List<CmWechatApplicationPO>
     */
    List<CmWechatApplicationPO> selectApplicationConfigList();
}