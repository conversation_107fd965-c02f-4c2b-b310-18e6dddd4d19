package com.howbuy.crm.wechat.dao.mapper;

import com.howbuy.crm.wechat.dao.po.CmWechatCompanyPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2025/7/24 8:35
 * @since JDK 1.8
 */
@Mapper
public interface CmWechatCompanyMapper {
    int deleteByPrimaryKey(String companyNo);

    int insert(CmWechatCompanyPO record);

    int insertSelective(CmWechatCompanyPO record);

    CmWechatCompanyPO selectByPrimaryKey(String companyNo);

    int updateByPrimaryKeySelective(CmWechatCompanyPO record);

    int updateByPrimaryKey(CmWechatCompanyPO record);



    /**
     * 根据corpId查询有效的公司配置信息
     * @param corpId
     * @return
     */
    CmWechatCompanyPO selectConfigByCorpId(@Param("corpId") String corpId);

    /**
     * 根据companyNo查询有效的公司配置信息
     * @param companyNo
     * @return
     */
    CmWechatCompanyPO selectConfigByCompanyNo(@Param("companyNo") String companyNo);


    /**
     * 查询有效的公司配置信息列表
     * @return List<CmWechatCompanyPO>
     */
    List<CmWechatCompanyPO> selectConfigList();
}