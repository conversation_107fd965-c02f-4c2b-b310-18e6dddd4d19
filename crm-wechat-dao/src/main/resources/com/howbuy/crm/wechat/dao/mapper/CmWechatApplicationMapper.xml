<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.wechat.dao.mapper.CmWechatApplicationMapper">
  <resultMap id="BaseResultMap" type="com.howbuy.crm.wechat.dao.po.CmWechatApplicationPO">
    <!--@mbg.generated-->
    <!--@Table cm_wechat_application-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="application_code" jdbcType="VARCHAR" property="applicationCode" />
    <result column="application_desc" jdbcType="VARCHAR" property="applicationDesc" />
    <result column="company_no" jdbcType="VARCHAR" property="companyNo" />
    <result column="application_type" jdbcType="VARCHAR" property="applicationType" />
    <result column="access_secret" jdbcType="VARCHAR" property="accessSecret" />
    <result column="agent_id" jdbcType="VARCHAR" property="agentId" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
    <result column="rec_stat" jdbcType="VARCHAR" property="recStat" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, application_code, application_desc, company_no, application_type, access_secret,
    agent_id, creator, create_time, modifier, modify_time, rec_stat
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from cm_wechat_application
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from cm_wechat_application
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.howbuy.crm.wechat.dao.po.CmWechatApplicationPO" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into cm_wechat_application (application_code, application_desc,
    company_no, application_type, access_secret,
    agent_id, creator, create_time,
    modifier, modify_time, rec_stat
    )
    values (#{applicationCode,jdbcType=VARCHAR}, #{applicationDesc,jdbcType=VARCHAR},
    #{companyNo,jdbcType=VARCHAR}, #{applicationType,jdbcType=VARCHAR}, #{accessSecret,jdbcType=VARCHAR},
    #{agentId,jdbcType=VARCHAR}, #{creator,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
    #{modifier,jdbcType=VARCHAR}, #{modifyTime,jdbcType=TIMESTAMP}, #{recStat,jdbcType=VARCHAR}
    )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.howbuy.crm.wechat.dao.po.CmWechatApplicationPO" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into cm_wechat_application
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="applicationCode != null">
        application_code,
      </if>
      <if test="applicationDesc != null">
        application_desc,
      </if>
      <if test="companyNo != null">
        company_no,
      </if>
      <if test="applicationType != null">
        application_type,
      </if>
      <if test="accessSecret != null">
        access_secret,
      </if>
      <if test="agentId != null">
        agent_id,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="modifyTime != null">
        modify_time,
      </if>
      <if test="recStat != null">
        rec_stat,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="applicationCode != null">
        #{applicationCode,jdbcType=VARCHAR},
      </if>
      <if test="applicationDesc != null">
        #{applicationDesc,jdbcType=VARCHAR},
      </if>
      <if test="companyNo != null">
        #{companyNo,jdbcType=VARCHAR},
      </if>
      <if test="applicationType != null">
        #{applicationType,jdbcType=VARCHAR},
      </if>
      <if test="accessSecret != null">
        #{accessSecret,jdbcType=VARCHAR},
      </if>
      <if test="agentId != null">
        #{agentId,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="modifyTime != null">
        #{modifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="recStat != null">
        #{recStat,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.howbuy.crm.wechat.dao.po.CmWechatApplicationPO">
    <!--@mbg.generated-->
    update cm_wechat_application
    <set>
      <if test="applicationCode != null">
        application_code = #{applicationCode,jdbcType=VARCHAR},
      </if>
      <if test="applicationDesc != null">
        application_desc = #{applicationDesc,jdbcType=VARCHAR},
      </if>
      <if test="companyNo != null">
        company_no = #{companyNo,jdbcType=VARCHAR},
      </if>
      <if test="applicationType != null">
        application_type = #{applicationType,jdbcType=VARCHAR},
      </if>
      <if test="accessSecret != null">
        access_secret = #{accessSecret,jdbcType=VARCHAR},
      </if>
      <if test="agentId != null">
        agent_id = #{agentId,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="modifyTime != null">
        modify_time = #{modifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="recStat != null">
        rec_stat = #{recStat,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.howbuy.crm.wechat.dao.po.CmWechatApplicationPO">
    <!--@mbg.generated-->
    update cm_wechat_application
    set application_code = #{applicationCode,jdbcType=VARCHAR},
    application_desc = #{applicationDesc,jdbcType=VARCHAR},
    company_no = #{companyNo,jdbcType=VARCHAR},
    application_type = #{applicationType,jdbcType=VARCHAR},
    access_secret = #{accessSecret,jdbcType=VARCHAR},
    agent_id = #{agentId,jdbcType=VARCHAR},
    creator = #{creator,jdbcType=VARCHAR},
    create_time = #{createTime,jdbcType=TIMESTAMP},
    modifier = #{modifier,jdbcType=VARCHAR},
    modify_time = #{modifyTime,jdbcType=TIMESTAMP},
    rec_stat = #{recStat,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>



  <select id="selectConfigByApplicationCode" parameterType="string" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from cm_wechat_application
    where APPLICATION_CODE = #{applicationCode,jdbcType=VARCHAR}
    and REC_STAT = '1'
  </select>

  <select id="selectApplicationConfigList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from cm_wechat_application
    where REC_STAT = '1'
  </select>
</mapper>