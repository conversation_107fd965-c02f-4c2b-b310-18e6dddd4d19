### Controller 多企业微信账号改造 Review 报告

作者: hongdong.xie  
时间: 2025-08-11 14:19:57

#### 背景与约定
- **目标**: 原仅支持单企业微信账号（多个应用），改造为支持多个企业微信账号（用 `companyNo` 区分）+ 多应用。
- **兼容要求**: 为兼容产线，原始接口如不传 `companyNo`，需默认使用 `1`。
- **企业主体枚举**: `1=HOWBUY_WEALTH(好买财富)`, `2=HOWBUY_FUND(好买基金)`, `3=HOWBUY_HXM(好晓买)`。

---

### 一、已支持多企业账号的接口

- WechatCallbackController
  - GET/POST `/wechat/fundverify`：固定公司=2（通过路径区分，视为支持多企业）
  - GET/POST `/wechat/hxmverify`：固定公司=3（同上）
  - GET/POST `/wechat/wealthverify`：固定公司=1（同上）

- WechatGroupUserController
  - POST `/wechatgroupuser/queryusergrouplist`：请求体支持 `companyNo`，为空时默认=1（完全满足“多企业+默认=1”）

- WechatConfigController
  - POST `/wechatconfig/reloadcacheconfig`：请求体可传 `companyNo`；不传时“全量刷新”。已支持多企业，但“默认=1”兼容策略未对齐（见下文建议）。

- WechatDeptController
  - GET `/wechatdept/execute`：支持多公司参数 `companyNos`（如 "1,2"）批量执行；未传时无默认（见下文建议）。

- WechatCallbackController（统一入口）
  - GET/POST `/wechat/companyVerify`：读取 query `companyNo` 决定企业主体；当前未做默认=1（见下文建议）。

---

### 二、不支持多企业账号的接口（目前固定到某个主体或无公司维度）

- SendWechatController
  - GET `/wechatmsgsend/sendvoiceremind`
  - POST `/wechatmsgsend/querysendstatus`（DTO 无 `companyNo`）
  - POST `/wechatmsgsend/querysendstatuswithmemo`（DTO 无 `companyNo`）
  - GET `/wechatmsgsend/sendMessageByIds`
  - GET `/wechatmsgsend/buildMessageByIds`

- WechatCustRelationController
  - POST `/wechatcustrelation/selectrelationlistbyvo`（内部固定 `CompanyNoEnum.HOWBUY_WEALTH`）

- WechatGroupController
  - GET `/wechatgroup/getgroupinfobyuserid`（内部固定=1）
  - GET `/wechatgroup/getgroupinfobychatid`（无 `companyNo`；非动态模式下走内部服务未区分企业主体）

- WechatGroupUserController（以下 4 个）
  - POST `/wechatgroupuser/getgroupidbyhboneno`（内部固定=1）
  - POST `/wechatgroupuser/getjoingroupresult`（内部固定=1）
  - POST `/wechatgroupuser/getgroupbreakstatus`（无公司维度）
  - POST `/wechatgroupuser/queryusergroupinfobyhbone`（内部固定=1）

- WechatTransferEventDealController
  - POST `/wechattransfereventdeal/transfereventdeal`（无公司维度）
  - POST `/wechattransfereventdeal/testdfile`（无公司维度）

---

### 三、已支持但“默认不传=1”未落地的接口（需补齐默认值）

- WechatCallbackController
  - GET/POST `/wechat/companyVerify`：应在 `companyNo` 为空或非法时默认=1

- WechatExternalUserController
  - GET `/wechatexternaluser/getexternaluser`：当前 `companyNo` 为空直接返回 null，应默认=1

- WechatDeptController
  - GET `/wechatdept/execute`：当 `companyNos` 为空时，建议默认执行公司=1

- WechatConfigController
  - POST `/wechatconfig/reloadcacheconfig`：现逻辑为“未传则全量刷新”。若要与“默认=1”契约完全一致，建议：未传时默认1；如需保留“全量刷新”，请在 API 文档显式声明为特例。

---

### 四、整改建议清单（保持向后兼容：不传即默认=1）

1) 对“未支持多企业”的接口补充 `companyNo`：
   - SendWechatController：所有接口新增 `companyNo`（`GET` 用 query param；`POST` 用 RequestBody 字段）。
     - `QueryMessageSendStatusDTO` 需新增 `companyNo` 字段；Controller 中解析后默认=1。
   - WechatCustRelationController、WechatGroupController、WechatGroupUserController（除 `queryusergrouplist` 外）与 WechatTransferEventDealController：补充 `companyNo` 参数并在 Controller 中默认=1。

2) 对“已支持但未默认=1”的接口补齐默认：
   - `/wechat/companyVerify`、`/wechatexternaluser/getexternaluser`、`/wechatdept/execute`：当未传或非法 `companyNo` 时回落为 `CompanyNoEnum.HOWBUY_WEALTH`（1），并记录 warn 日志。
   - `/wechatconfig/reloadcacheconfig`：按产品决策选择“未传=1”或维持“全量刷新（特例）”，并在 API 文档中注明。

3) 统一 Controller 端兜底策略：
   - `CompanyNoEnum companyNoEnum = Optional.ofNullable(CompanyNoEnum.getEnum(companyNo)).orElse(CompanyNoEnum.HOWBUY_WEALTH);`
   - 非法值记录告警但不抛错，提升可用性。

4) 文档与监控：
   - 补充各接口的 `companyNo` 字段说明与默认策略；在关键入口（回调、发送、查询）增加 `companyNo` 日志打点，便于排查多主体混用问题。

---

### 附录：CompanyNoEnum 摘要
- 1: HOWBUY_WEALTH（好买财富）
- 2: HOWBUY_FUND（好买基金）
- 3: HOWBUY_HXM（好晓买）


