## 企业微信多企业/多应用/第三方配置治理方案

- 作者: hongdong.xie
- 日期: 2025-08-11 13:03:24
- 适用范围: `crm-wechat-service`、`crm-wechat-remote`、`crm-wechat-dao`、`crm-wechat-client`

### 1. 背景与现状

- 现状
  - 企业微信相关关键参数（如 `corpid`、`encodingaeskey`、`token`、`corpsecret` 等）集中定义在 `WechatConfig` 中，以静态字段方式暴露，并通过配置文件注入。
  - 代码中大量直接引用 `WechatConfig.wealthCorpid`、`WechatConfig.fundCorpid` 等静态字段，且枚举 `CompanyWechatEnum`、`WechatSecretEnum` 等对特定企业/应用写死绑定。
  - 回调 Controller 以企业维度拆分（如财富/基金两个方法），扩展到更多企业/应用后方法会持续膨胀。

- 痛点
  - 可扩展性差：新增企业/应用/第三方套件需要改代码、发版。
  - 配置治理弱：配置分散在 properties，无法统一治理、灰度、权限管控与审计。
  - 运行期不可热变更：静态字段一旦初始化难以热更新，切换成本高。
  - 安全性不足：密钥明文配置，缺少统一加密与轮转策略。

### 2. 目标

- 支持多企业（多个 `corpid`）与多应用（多 `agentId`/`secret`），以及第三方应用（Suite）。
- 所有敏感配置统一托管至 Nacos（支持密文），服务运行期可热更新，具备回滚能力。
- 对业务层提供清晰、稳定的“租户化”配置访问接口，避免业务感知存储实现细节。
- 渐进式迁移：保留兼容层，允许在不大面积改动业务代码前提下逐步迁移。

### 3. 总体方案概述

- 配置中心
  - 在 Nacos 使用一个 DataId（例如：`crm-wechat/wechat-multi-tenant.json`），集中管理多企业/多应用配置。
  - 所有 secrets 支持加密存储（使用 Nacos KMS/三方 KMS 或内部 AES 加密），运行期解密。

- 运行期装载与缓存
  - 在 `crm-wechat-service` 新增 `WechatTenantConfigLoader` 组件，负责从 Nacos 拉取 JSON、解析为内存模型，并监听配置变更热更新。
  - 使用双缓冲或版本化 `ConcurrentHashMap` 缓存，保证读取无锁/低锁、更新无抖动。

- 上下文与路由
  - 新增 `WechatTenantResolver`，根据请求上下文（header、param、path、消息体）解析当前企业/应用维度的“租户键”（如 `corpId`/`companyNo`/自定义键）。
  - Controller/Service 层通过 `WechatCredentialService` 按上下文获取所需的 `corpid`、`agentId`、`secret`、`token`、`encodingaeskey` 等。

- 兼容迁移
  - 保留 `WechatConfig` 作为“默认企业”兼容层，其静态字段在启动时由新的配置加载器填充默认租户，保障存量代码可运行。
  - 新代码逐步改为依赖 `WechatCredentialService`，最终移除静态字段直接访问。

### 4. Nacos 配置模型（JSON）

示例 DataId: `crm-wechat/wechat-multi-tenant.json`

```json
{
  "version": "1.0.0",
  "defaultTenantKey": "wealth", 
  "tenants": {
    "wealth": {
      "corpId": "wwxxxxxxxxxxxxxx",
      "companyNo": "1",
      "name": "好买财富",
      "env": "prod",
      "callback": {
        "token": "********",
        "encodingAesKey": "***********************"
      },
      "apps": {
        "CUSTOMER_CONTACT": {
          "agentId": "",
          "secret": "********",
          "http": { "connectTimeoutMs": 2000, "readTimeoutMs": 5000 },
          "tokenTtlSeconds": 7000,
          "scopes": ["external_contact"],
          "enabled": true
        },
        "CRM": {
          "agentId": "1000001",
          "secret": "********",
          "enabled": true
        },
        "PORTRAIT": {
          "agentId": "1000002",
          "secret": "********",
          "enabled": true
        },
        "ORG_SERVICE_NOTICE": {
          "agentId": "1000003",
          "secret": "********",
          "enabled": true
        }
      },
      "thirdParty": {
        "suiteId": "wwsuite-xxx",
        "suiteSecret": "********",
        "token": "********",
        "encodingAesKey": "***********************",
        "enabled": false
      }
    },
    "fund": {
      "corpId": "wwyyyyyyyyyyyyyy",
      "companyNo": "2",
      "name": "好买基金",
      "env": "prod",
      "callback": {
        "token": "********",
        "encodingAesKey": "***********************"
      },
      "apps": {
        "CUSTOMER_CONTACT": { "secret": "********", "enabled": true }
      }
    }
  }
}
```

关键说明
- tenants 的 key（如 `wealth`/`fund`）为内部租户键；同时为每个租户保留 `corpId` 与 `companyNo` 以兼容现有逻辑。
- apps 的 key 使用统一枚举（建议：`WechatAppType`），避免散乱的业务含义命名。
- thirdParty 为可选配置，用于第三方套件（如服务商/suite 应用）。

### 5. 核心类设计（建议包路径 `com.howbuy.crm.wechat.service.config`/`cacheservice`）

- WechatTenantConfig
  - 描述一个企业租户：corpId、companyNo、callback(token/encodingAesKey)、apps(Map<WechatAppType, WechatAppCredential>)、thirdParty。

- WechatAppCredential
  - 描述一个应用的凭证：agentId、secret、httpPolicy、tokenTtlSeconds、scopes、enabled。

- WechatTenantConfigLoader
  - 从 Nacos 拉取 JSON 配置、解析、校验；监听配置变更并热更新内存快照（版本化/双缓冲）。
  - 支持敏感字段解密（集成内部 AES/外部 KMS）。

- WechatTenantIndex
  - 建立多维索引：byTenantKey、byCorpId、byCompanyNo，便于不同入口快速定位租户。

- WechatTenantResolver
  - 从请求上下文解析当前租户键与应用类型：
    - 回调入口：按路径参数 `/{tenantKey}/callback` 或按 header/param 解析。
    - 业务入口：从登录态、header（如 `X-Corp-Id`）、请求参数或 DB 中的公司维度字段推导。

- WechatCredentialService（门面）
  - getCorpId(tenantKey)
  - getCallbackConfig(tenantKey)
  - getAppCredential(tenantKey, appType)
  - getAccessToken(tenantKey, appType)（若集中托管 token，可在此实现获取与缓存）

- AccessTokenCache（可选）
  - 维护每个（tenantKey, appType）的 `access_token` 短期缓存：过期双阈值策略（例如过期阈值与预刷新阈值），单飞请求，异常降级。

### 6. 热更新与一致性

- 变更监听：Nacos 配置发生变更后，解析新版本，原子性替换内存快照（如使用 `AtomicReference<Map>`）。
- 读写隔离：读路径只访问快照，避免阻塞；更新路径在后台完成解析/校验后一次性切换。
- 变更回调：对关键依赖（如 token 管理器）提供变更回调，触发必要的清理/重建。

### 7. 兼容与迁移策略

- 保留兼容层
  - 启动时选择一个默认租户（如 `defaultTenantKey=wealth`），将其字段回填到 `WechatConfig` 的静态变量，保证存量代码无感运行。
  - 新增 `Deprecated` 提示，约定新代码不再直接使用 `WechatConfig` 静态字段。

- 迁移建议顺序
  1) Controller 回调入口合并：将 `fundVerify`/`wealthVerify` 合并为 `/{tenantKey}/callback`；
  2) 将业务中对 `WechatConfig.xxx` 的直接引用替换为 `WechatCredentialService`；
  3) 更新 `CompanyWechatEnum`/`WechatSecretEnum`：由“常量绑定”迁移为“查询绑定”（通过租户与应用类型动态解析）；
  4) 删除不再需要的静态字段与强绑定枚举。

### 8. 安全与运维

- 密钥安全
  - Nacos 中的敏感字段使用 `ENC(...)` 或者 KMS 托管；服务侧集成解密器，加载时解密。
  - 支持密钥轮换：新增版本写入 Nacos，切换租户配置引用即可生效；旧版本保留一段时间回滚。

- 审计与权限
  - Nacos 命名空间/分组划分，限制写权限；开启操作审计与变更通知。

- 降级
  - 加载失败使用上一次有效快照；必要时可在本地挂载只读兜底 JSON。

### 9. 测试计划

- 单元测试
  - JSON 解析与校验；索引构建；租户解析；凭证获取；热更新替换；token 缓存策略。

- 集成测试
  - 回调验签（不同租户）；不同租户不同应用的调用鉴权；热更新后调用连续性。

- 回归测试
  - 兼容层验证：默认租户下旧逻辑行为不变。

### 10. 落地步骤清单（建议两周内完成）

1) 新增配置模型与 Loader/Service 门面，接入 Nacos，完成热更新与快照缓存。
2) 引入 `WechatTenantResolver`，统一解析租户上下文。
3) 回调 Controller 合并为通用入口，保留旧入口一段时间 302/代理。
4) 新功能全面使用 `WechatCredentialService`；老代码按模块逐步替换。
5) 安全改造：Nacos 敏感字段加密、KMS 对接、密钥轮换演练。
6) 压测与回归；切流与回滚预案演练。

### 11. 数据库托管（推荐）

- 推荐将多企业/多应用配置“落库”，结合 Nacos 做“下发/热更新/灰度/回滚”。
- 好处：可视化运营、权限审计、历史版本留存、密钥轮换可控；支持将来运营控制台。
- 已产出 SQL 初始化脚本：`crm-wechat-dao/src/main/resources/db/init.sql`。

建议 DDL（MySQL）：

```sql
CREATE TABLE `wechat_corp_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `tenant_key` varchar(64) NOT NULL COMMENT '内部租户键，如 wealth/fund',
  `corp_id` varchar(64) NOT NULL COMMENT '企业微信 corpId',
  `company_no` varchar(32) DEFAULT NULL COMMENT '公司编号，兼容现有逻辑',
  `corp_name` varchar(128) DEFAULT NULL COMMENT '企业名称',
  `callback_token` varchar(256) DEFAULT NULL COMMENT '回调 Token（建议密文存储）',
  `callback_encoding_aes_key` varchar(256) DEFAULT NULL COMMENT '回调 EncodingAESKey（建议密文存储）',
  `env` varchar(32) DEFAULT 'prod' COMMENT '环境标识',
  `status` tinyint(4) DEFAULT 1 COMMENT '1:启用 0:停用',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_tenant_key` (`tenant_key`),
  UNIQUE KEY `uk_corp_id` (`corp_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `wechat_app_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `tenant_key` varchar(64) NOT NULL COMMENT '内部租户键',
  `app_type` varchar(64) NOT NULL COMMENT '应用类型：如 CUSTOMER_CONTACT/CRM/PORTRAIT/ORG_SERVICE_NOTICE',
  `agent_id` varchar(64) DEFAULT NULL COMMENT '企业微信 agentId',
  `secret` varchar(256) DEFAULT NULL COMMENT '应用 secret（建议密文存储）',
  `enabled` tinyint(4) DEFAULT 1 COMMENT '1:启用 0:停用',
  `token_ttl_seconds` int(11) DEFAULT 7000 COMMENT 'access_token TTL 建议值',
  `http_connect_timeout_ms` int(11) DEFAULT 2000 COMMENT 'HTTP连接超时',
  `http_read_timeout_ms` int(11) DEFAULT 5000 COMMENT 'HTTP读超时',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_tenant_app` (`tenant_key`,`app_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `wechat_suite_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `tenant_key` varchar(64) NOT NULL COMMENT '内部租户键（如按企业分配套件）',
  `suite_id` varchar(128) NOT NULL COMMENT '套件ID',
  `suite_secret` varchar(256) DEFAULT NULL COMMENT '套件Secret（建议密文存储）',
  `token` varchar(256) DEFAULT NULL COMMENT '回调 Token（建议密文存储）',
  `encoding_aes_key` varchar(256) DEFAULT NULL COMMENT '回调 EncodingAESKey（建议密文存储）',
  `enabled` tinyint(4) DEFAULT 0 COMMENT '1:启用 0:停用',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_tenant_suite` (`tenant_key`, `suite_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

### 12. 与现有代码的映射关系（迁移对照）

- 静态字段映射
  - `WechatConfig.wealthCorpid` -> `credentialService.getCorpId("wealth")`
  - `WechatConfig.fundCorpid` -> `credentialService.getCorpId("fund")`
  - `WechatConfig.wealthEncodingAESkey` -> `credentialService.getCallbackConfig("wealth").getEncodingAesKey()`
  - `WechatConfig.fundEncodingAESkey` -> `credentialService.getCallbackConfig("fund").getEncodingAesKey()`
  - `WechatConfig.crmCorpSecret` -> `credentialService.getAppCredential("wealth", CRM).getSecret()`
  - ...

- 回调入口
  - 由 `fundVerify`/`wealthVerify` 合并为 `/{tenantKey}/callback`，按 `tenantKey` 选择对应 token/aesKey 进行验签。

### 13. 风险与缓解

- 配置回滚：每次发布前在 Nacos 复制 DataId 创建“版本快照”；出问题快速回滚。
- 热更新失败：保留最近一次有效快照，加载失败时降级为只读快照；必要时采用本地只读备份。
- 密钥泄露：强制密文存储、最小权限访问、密钥轮转演练与审计。

---

如需实施细节（类图/时序图/接口签名/伪代码），可在本方案基础上拆分为开发任务清单逐项落地。


